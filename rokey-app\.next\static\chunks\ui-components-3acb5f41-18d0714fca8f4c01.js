"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6060],{8413:(e,s,a)=>{a.d(s,{A:()=>i});var t=a(95155),r=a(12115),l=a(57514);let n=new Map;function i(e){let{configId:s,onRetry:a,className:i="",disabled:c=!1}=e,[d,o]=(0,r.useState)(!1),[m,x]=(0,r.useState)([]),[u,g]=(0,r.useState)(!1),[h,p]=(0,r.useState)(!1),y=(0,r.useRef)(null),f=(0,r.useCallback)(async function(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0];if(s){if(e){let e=n.get(s);if(e&&Date.now()-e.timestamp<3e5){x(e.keys),p(!0);return}}g(!0);try{let e=await fetch("/api/keys?custom_config_id=".concat(s));if(e.ok){let a=(await e.json()).filter(e=>"active"===e.status);n.set(s,{keys:a,timestamp:Date.now()}),x(a),p(!0)}}catch(e){}finally{g(!1)}}},[s]);(0,r.useEffect)(()=>{s&&!h&&f(!0)},[s,f,h]),(0,r.useEffect)(()=>{let e=e=>{y.current&&!y.current.contains(e.target)&&o(!1)};return d&&document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[d]);let b=e=>{o(!1),a(e)};return(0,t.jsxs)("div",{className:"relative ".concat(i),ref:y,children:[(0,t.jsxs)("button",{onClick:()=>{d||0!==m.length||h||f(!0),o(!d)},disabled:c,className:"\n          flex items-center space-x-1 p-1.5 rounded transition-all duration-200 cursor-pointer\n          ".concat(c?"text-gray-300 cursor-not-allowed":"text-gray-500 hover:text-gray-700 hover:bg-white/20","\n        "),title:"Retry with different model",children:[(0,t.jsx)(l.E,{className:"w-4 h-4 stroke-2 ".concat(u?"animate-spin":"")}),(0,t.jsx)(l.D,{className:"w-3 h-3 stroke-2"})]}),d&&(0,t.jsx)("div",{className:"absolute bottom-full left-0 mb-1 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-50 animate-scale-in origin-bottom-left",children:(0,t.jsxs)("div",{className:"py-1",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between px-3 py-2 border-b border-gray-100",children:[(0,t.jsx)("span",{className:"text-xs font-medium text-gray-600",children:"Retry Options"}),(0,t.jsx)("button",{onClick:e=>{e.stopPropagation(),f(!1)},disabled:u,className:"p-1 text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50",title:"Refresh available models",children:(0,t.jsx)(l.E,{className:"w-3 h-3 ".concat(u?"animate-spin":"")})})]}),(0,t.jsxs)("button",{onClick:()=>b(),className:"w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2",children:[(0,t.jsx)(l.E,{className:"w-4 h-4 text-gray-500"}),(0,t.jsx)("span",{children:"Retry with same model"})]}),(m.length>0||u)&&(0,t.jsx)("div",{className:"border-t border-gray-100 my-1"}),u&&(0,t.jsxs)("div",{className:"px-3 py-2 text-sm text-gray-500 flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-4 h-4 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin"}),(0,t.jsx)("span",{children:"Loading models..."})]}),m.map(e=>(0,t.jsxs)("button",{onClick:()=>b(e.id),className:"w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex flex-col disabled:opacity-50",disabled:u,children:[(0,t.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,t.jsx)("span",{className:"font-medium",children:e.label}),(0,t.jsx)("span",{className:"text-xs text-gray-500 capitalize",children:e.provider})]}),(0,t.jsxs)("div",{className:"text-xs text-gray-500 mt-0.5",children:["Temperature: ",e.temperature]})]},e.id)),!u&&0===m.length&&h&&(0,t.jsx)("div",{className:"px-3 py-2 text-sm text-gray-500",children:"No alternative models available"}),m.length>0&&!u&&(0,t.jsxs)("div",{className:"px-3 py-1 text-xs text-gray-400 border-t border-gray-100 flex items-center justify-between",children:[(0,t.jsxs)("span",{children:[m.length," model",1!==m.length?"s":""," available"]}),(()=>{let e=n.get(s);return e&&Date.now()-e.timestamp<3e5?(0,t.jsx)("span",{className:"text-green-500 text-xs",children:"●"}):null})()]})]})})]})}},13741:(e,s,a)=>{a.d(s,{$:()=>n});var t=a(95155),r=a(12115),l=a(74338);let n=(0,r.forwardRef)((e,s)=>{let{className:a="",variant:r="default",size:n="default",loading:i=!1,icon:c,iconPosition:d="left",children:o,disabled:m,...x}=e,u={default:"h-5 w-5",sm:"h-4 w-4",md:"h-5 w-5",lg:"h-6 w-6",icon:"h-5 w-5"},g=m||i;return(0,t.jsxs)("button",{ref:s,className:"".concat("inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-white dark:focus:ring-offset-gray-900 disabled:opacity-50 disabled:cursor-not-allowed"," ").concat({default:"bg-orange-600 text-white hover:bg-orange-700",primary:"bg-orange-600 text-white hover:bg-orange-700",secondary:"bg-gray-100 text-gray-900 hover:bg-gray-200",outline:"border border-gray-300 bg-white hover:bg-gray-50 text-gray-700",ghost:"text-gray-700 hover:bg-gray-100 focus:ring-gray-500",destructive:"bg-red-600 text-white hover:bg-red-700 hover:shadow-md focus:ring-red-500",danger:"bg-red-600 text-white hover:bg-red-700 hover:shadow-md focus:ring-red-500"}[r]," ").concat({default:"px-4 py-2.5 text-sm",sm:"px-3 py-2 text-sm",md:"px-4 py-2.5 text-sm",lg:"px-6 py-3 text-base",icon:"h-10 w-10"}[n]," ").concat(a),disabled:g,...x,children:[i&&(0,t.jsx)(l.Ay,{size:"lg"===n?"md":"sm",className:"mr-2"}),!i&&c&&"left"===d&&(0,t.jsx)("span",{className:"".concat(u[n]," mr-2"),children:c}),o,!i&&c&&"right"===d&&(0,t.jsx)("span",{className:"".concat(u[n]," ml-2"),children:c})]})});n.displayName="Button"},14446:(e,s,a)=>{a.d(s,{Ay:()=>r,CE:()=>l});var t=a(95155);function r(){return(0,t.jsx)("div",{className:"min-h-screen bg-cream animate-fade-in",children:(0,t.jsxs)("div",{className:"container mx-auto px-6 py-8",children:[(0,t.jsx)("div",{className:"mb-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("div",{className:"flex items-center space-x-4",children:(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-20 animate-pulse"})}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded w-64 mb-2 animate-pulse"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-48 animate-pulse"})]})]})}),(0,t.jsx)("div",{className:"mb-6",children:(0,t.jsx)("div",{className:"h-12 bg-gray-200 rounded-lg animate-pulse"})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,t.jsx)("div",{className:"lg:col-span-1",children:(0,t.jsxs)("div",{className:"card p-6",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-32 mb-6 animate-pulse"}),(0,t.jsx)("div",{className:"space-y-4",children:[1,2,3,4].map(e=>(0,t.jsx)("div",{className:"border border-gray-200 rounded-xl p-4 animate-pulse",children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded-lg animate-pulse"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("div",{className:"h-5 bg-gray-200 rounded w-32 mb-2 animate-pulse"}),(0,t.jsx)("div",{className:"h-3 bg-gray-200 rounded w-24 mb-2 animate-pulse"}),(0,t.jsx)("div",{className:"h-3 bg-gray-200 rounded w-full animate-pulse"})]})]})},e))})]})}),(0,t.jsx)("div",{className:"lg:col-span-2",children:(0,t.jsxs)("div",{className:"card p-8 min-h-[600px]",children:[(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("div",{className:"h-7 bg-gray-200 rounded w-48 mb-3 animate-pulse"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-full animate-pulse"})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-24 mb-2 animate-pulse"}),(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-32 mb-2 animate-pulse"}),(0,t.jsx)("div",{className:"h-32 bg-gray-200 rounded animate-pulse"})]})]}),(0,t.jsxs)("div",{className:"border-t border-gray-200 pt-6",children:[(0,t.jsx)("div",{className:"h-5 bg-gray-200 rounded w-40 mb-4 animate-pulse"}),(0,t.jsx)("div",{className:"space-y-3",children:[1,2,3].map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 border border-gray-200 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded animate-pulse"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-24 mb-1 animate-pulse"}),(0,t.jsx)("div",{className:"h-3 bg-gray-200 rounded w-32 animate-pulse"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-16 animate-pulse"}),(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-12 animate-pulse"})]})]},e))})]}),(0,t.jsxs)("div",{className:"border-t border-gray-200 pt-6",children:[(0,t.jsx)("div",{className:"h-5 bg-gray-200 rounded w-48 mb-4 animate-pulse"}),(0,t.jsx)("div",{className:"grid grid-cols-5 gap-3 mb-4",children:[1,2,3,4,5].map(e=>(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-16 mx-auto mb-2 animate-pulse"}),(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded animate-pulse"})]},e))}),(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded w-32 animate-pulse"})]}),(0,t.jsx)("div",{className:"border-t border-gray-200 pt-6",children:(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded w-40 animate-pulse"})})]})]})})]})]})})}function l(){return(0,t.jsx)("div",{className:"min-h-screen bg-cream animate-fade-in",children:(0,t.jsxs)("div",{className:"container mx-auto px-6 py-8",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-20 animate-pulse"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-48 mb-1 animate-pulse"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-32 animate-pulse"})]})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8",children:[1,2,3,4].map(e=>(0,t.jsx)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-8 h-8 bg-gray-200 rounded animate-pulse"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-20 mb-1 animate-pulse"}),(0,t.jsx)("div",{className:"h-3 bg-gray-200 rounded w-16 animate-pulse"})]})]})},e))}),(0,t.jsxs)("div",{className:"bg-white rounded-lg border border-gray-200 p-6",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-40 mb-4 animate-pulse"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded animate-pulse"}),(0,t.jsx)("div",{className:"h-20 bg-gray-200 rounded animate-pulse"}),(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded w-32 animate-pulse"})]})]})]})})}a(12115)},26126:(e,s,a)=>{a.d(s,{E:()=>l});var t=a(95155);a(12115);let r=(0,a(74466).F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-orange-600 text-white hover:bg-orange-700",secondary:"border-transparent bg-gray-100 text-gray-900 hover:bg-gray-200",destructive:"border-transparent bg-red-600 text-white hover:bg-red-700",outline:"text-gray-700 border-gray-300"}},defaultVariants:{variant:"default"}});function l(e){let{className:s,variant:a,...l}=e;return(0,t.jsx)("div",{className:"".concat(r({variant:a})," ").concat(s||""),...l})}},38050:(e,s,a)=>{a.d(s,{default:()=>i});var t=a(12115),r=a(35695),l=a(21826),n=a(44042);function i(e){let{enableUserBehaviorTracking:s=!0,enableNavigationTracking:a=!0,enableInteractionTracking:i=!0}=e,c=(0,r.usePathname)(),d=(0,t.useRef)(""),o=(0,t.useRef)(0),{exportMetrics:m}=(0,n.D)("PerformanceTracker");return(0,t.useEffect)(()=>{if(!a)return;let e=d.current;e&&e!==c&&(l.zf.trackNavigation(e,c),performance.now(),o.current),d.current=c,o.current=performance.now()},[c,a]),(0,t.useEffect)(()=>{if(!i)return;let e=e=>{let s=e.target;if("mouseenter"===e.type&&"A"===s.tagName){let e=s.getAttribute("href");e&&e.startsWith("/")&&l.zf.schedulePrefetch(e)}if("click"===e.type&&("BUTTON"===s.tagName||s.closest("button"))){var a;let e=(null==(a=s.textContent)?void 0:a.trim())||"Unknown";e.toLowerCase().includes("get started")||e.toLowerCase().includes("sign up")?l.zf.schedulePrefetch("/auth/signup"):e.toLowerCase().includes("pricing")?l.zf.schedulePrefetch("/pricing"):e.toLowerCase().includes("features")&&l.zf.schedulePrefetch("/features")}};return document.addEventListener("mouseenter",e,!0),document.addEventListener("click",e,!0),()=>{document.removeEventListener("mouseenter",e,!0),document.removeEventListener("click",e,!0)}},[i]),(0,t.useEffect)(()=>{let e;if(!s)return;let a=!1,t=0,r=()=>{a||(a=!0,performance.now());let s=window.scrollY/(document.body.scrollHeight-window.innerHeight)*100;t=Math.max(t,s),clearTimeout(e),e=setTimeout(()=>{a=!1,performance.now(),t>80&&("/"===c?(l.zf.schedulePrefetch("/pricing"),l.zf.schedulePrefetch("/features")):"/features"===c&&l.zf.schedulePrefetch("/auth/signup")),t=0},150)},n=performance.now(),i=()=>{performance.now()-n>1e4&&("/"===c?l.zf.schedulePrefetch("/auth/signup"):"/pricing"===c&&l.zf.schedulePrefetch("/auth/signup"))};window.addEventListener("scroll",r,{passive:!0});let d=()=>{document.hidden&&i()};document.addEventListener("visibilitychange",d);let o=()=>{i()};return window.addEventListener("beforeunload",o),()=>{clearTimeout(e),window.removeEventListener("scroll",r),document.removeEventListener("visibilitychange",d),window.removeEventListener("beforeunload",o),i()}},[c,s,m]),(0,t.useEffect)(()=>{if("PerformanceObserver"in window){let e=new PerformanceObserver(e=>{let s=e.getEntries();s[s.length-1].startTime});e.observe({entryTypes:["largest-contentful-paint"]});let s=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{e.processingStart&&e.startTime&&(e.processingStart,e.startTime)})});s.observe({entryTypes:["first-input"]});let a=0,t=new PerformanceObserver(e=>{e.getEntries().forEach(e=>{e.hadRecentInput||(a+=e.value)})});return t.observe({entryTypes:["layout-shift"]}),()=>{e.disconnect(),s.disconnect(),t.disconnect()}}},[]),null}},50956:(e,s,a)=>{a.d(s,{A:()=>c});var t=a(95155),r=a(12115),l=a(6874),n=a.n(l),i=a(35695);function c(e){let{href:s,children:a,className:l="",prefetch:c=!0}=e,d=(0,i.useRouter)();return(0,t.jsx)(n(),{href:s,className:l,onClick:e=>{e.preventDefault(),(0,r.startTransition)(()=>{d.push(s)})},prefetch:c,children:a})}},60993:(e,s,a)=>{a.d(s,{Jg:()=>g,yA:()=>f,sU:()=>u});var t=a(95155);a(12115);var r=a(83298),l=a(74857),n=a(55020),i=a(39499),c=a(35695);let d={custom_roles:"Custom Roles",knowledge_base:"Knowledge Base",advanced_routing:"Advanced Routing",prompt_engineering:"Prompt Engineering",semantic_caching:"Semantic Caching",configurations:"API Configurations"},o=e=>{for(let s of["starter","professional","enterprise"]){let a=l.v7[s];switch(e){case"custom_roles":if(a.limits.canUseCustomRoles)return s;break;case"knowledge_base":if(a.limits.canUseKnowledgeBase)return s;break;case"advanced_routing":if(a.limits.canUseAdvancedRouting)return s;break;case"prompt_engineering":if(a.limits.canUsePromptEngineering)return s;break;case"semantic_caching":if(a.limits.canUseSemanticCaching)return s;break;case"configurations":if(a.limits.configurations>l.v7.free.limits.configurations)return s}}return"starter"};function m(e){let{feature:s,currentTier:a,customMessage:m,size:x="md",variant:u="card"}=e,{createCheckoutSession:g}=(0,r.R)(),h=(0,c.useRouter)(),p=o(s),y=l.v7[p],f=d[s],b=async()=>{try{"starter"===p?await g("starter"):"professional"===p?await g("professional"):h.push("/pricing")}catch(e){h.push("/pricing")}};return(0,t.jsx)(n.PY1.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"".concat({card:"bg-gradient-to-br from-orange-50 to-orange-100 border border-orange-200 rounded-xl shadow-sm",banner:"bg-orange-100 border-l-4 border-orange-500 rounded-r-lg",inline:"bg-orange-50 border border-orange-200 rounded-lg"}[u]," ").concat({sm:"p-4 text-sm",md:"p-6 text-base",lg:"p-8 text-lg"}[x]),children:(0,t.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("div",{className:"w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center",children:(0,t.jsx)(i.JD,{className:"w-5 h-5 text-white"})})}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:[f," - Premium Feature"]}),(0,t.jsx)("p",{className:"text-gray-700 mb-4",children:m||"".concat(f," is available starting with the ").concat(y.name," plan. \n               Upgrade to unlock this powerful feature and enhance your RouKey experience.")}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("button",{onClick:b,className:"inline-flex items-center px-4 py-2 bg-orange-600 text-white text-sm font-medium rounded-lg hover:bg-orange-700 transition-colors duration-200",children:[(0,t.jsx)(i.Kp,{className:"w-4 h-4 mr-2"}),"Upgrade to ",y.name]}),(0,t.jsxs)("button",{onClick:()=>h.push("/pricing"),className:"inline-flex items-center px-4 py-2 text-orange-600 text-sm font-medium hover:text-orange-700 transition-colors duration-200",children:[(0,t.jsx)(i.Gg,{className:"w-4 h-4 mr-2"}),"View All Plans"]})]})]})]})})}var x=a(74338);function u(e){let{feature:s,children:a,fallback:n,showUpgradePrompt:i=!0,customMessage:c,currentCount:d=0}=e,{subscriptionStatus:o,loading:u}=(0,r.R)();if(u)return(0,t.jsx)(x.Ay,{});let g=(null==o?void 0:o.tier)||"free";if("configurations"===s){if(d<(0,l.zX)(g).limits.configurations)return(0,t.jsx)(t.Fragment,{children:a})}else if((0,l.Nu)(g,s))return(0,t.jsx)(t.Fragment,{children:a});return n?(0,t.jsx)(t.Fragment,{children:n}):i?(0,t.jsx)(m,{feature:s,currentTier:g,customMessage:c}):null}function g(e){let{current:s,limit:a,label:r,tier:l,showUpgradeHint:i=!0,className:d=""}=e,o=(0,c.useRouter)(),m=a>=999999,x=m?0:s/a*100,u=x>=80,g=s>=a&&!m;return(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm ".concat(d),children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)("span",{className:"text-gray-600",children:[r,":"]}),!m&&(0,t.jsx)("div",{className:"w-16 bg-gray-200 rounded-full h-1.5",children:(0,t.jsx)(n.PY1.div,{className:"h-1.5 rounded-full ".concat(m?"bg-green-500":g?"bg-red-500":u?"bg-yellow-500":"bg-green-500"),initial:{width:0},animate:{width:"".concat(Math.min(x,100),"%")},transition:{duration:.5,ease:"easeOut"}})})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)("span",{className:"text-xs font-medium ".concat(m?"text-green-600":g?"text-red-600":u?"text-yellow-600":"text-green-600"),children:m?"Unlimited":"".concat(s,"/").concat(a)}),(g||u)&&i&&(0,t.jsx)("button",{className:"text-xs text-orange-600 hover:text-orange-700 underline ml-2",onClick:()=>{o.push("/billing")},children:"Upgrade"})]})]})}a(47321);var h=a(17974);let p={free:{name:"Free",color:"bg-gray-100 text-gray-800 border-gray-300",icon:h.Zu,iconColor:"text-gray-600"},starter:{name:"Starter",color:"bg-blue-100 text-blue-800 border-blue-300",icon:h.Gg,iconColor:"text-blue-600"},professional:{name:"Professional",color:"bg-orange-100 text-orange-800 border-orange-300",icon:h.BZ,iconColor:"text-orange-600"},enterprise:{name:"Enterprise",color:"bg-purple-100 text-purple-800 border-purple-300",icon:h.OR,iconColor:"text-purple-600"}},y={sm:{container:"px-2 py-1 text-xs",icon:"w-3 h-3"},md:{container:"px-3 py-1 text-sm",icon:"w-4 h-4"},lg:{container:"px-4 py-2 text-base",icon:"w-5 h-5"}};function f(e){let{tier:s,size:a="md",showIcon:r=!0,className:l=""}=e,n=p[s],i=y[a],c=n.icon;return(0,t.jsxs)("span",{className:"\n      inline-flex items-center space-x-1 font-medium border rounded-full\n      ".concat(n.color," \n      ").concat(i.container,"\n      ").concat(l,"\n    "),children:[r&&(0,t.jsx)(c,{className:"".concat(i.icon," ").concat(n.iconColor)}),(0,t.jsx)("span",{children:n.name})]})}},64198:(e,s,a)=>{a.d(s,{N9:()=>i,dj:()=>c});var t=a(95155),r=a(12115),l=a(21884);let n=e=>{let{toast:s,onRemove:a}=e,[n,i]=(0,r.useState)(!1),[c,d]=(0,r.useState)(!1);(0,r.useEffect)(()=>{let e=setTimeout(()=>i(!0),10);return()=>clearTimeout(e)},[]),(0,r.useEffect)(()=>{if(s.duration&&s.duration>0){let e=setTimeout(()=>{o()},s.duration);return()=>clearTimeout(e)}},[s.duration]);let o=()=>{d(!0),setTimeout(()=>{a(s.id)},300)};return(0,t.jsx)("div",{className:"\n        transform transition-all duration-300 ease-in-out\n        ".concat(n&&!c?"translate-x-0 opacity-100":"translate-x-full opacity-0","\n        ").concat((()=>{let e="glass rounded-xl p-4 shadow-lg border";switch(s.type){case"success":return"".concat(e," border-green-500/20 bg-green-500/10");case"error":return"".concat(e," border-red-500/20 bg-red-500/10");case"warning":return"".concat(e," border-yellow-500/20 bg-yellow-500/10");case"info":return"".concat(e," border-blue-500/20 bg-blue-500/10")}})(),"\n      "),children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(()=>{switch(s.type){case"success":return(0,t.jsx)(l.C1,{className:"h-5 w-5 text-green-400"});case"error":return(0,t.jsx)(l.qh,{className:"h-5 w-5 text-red-400"});case"warning":return(0,t.jsx)(l.Pi,{className:"h-5 w-5 text-yellow-400"});case"info":return(0,t.jsx)(l.KS,{className:"h-5 w-5 text-blue-400"})}})()}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-white",children:s.title}),s.message&&(0,t.jsx)("p",{className:"text-sm text-gray-300 mt-1",children:s.message})]}),(0,t.jsx)("button",{onClick:o,className:"flex-shrink-0 p-1 rounded-lg hover:bg-white/10 transition-colors duration-200",children:(0,t.jsx)(l.fK,{className:"h-4 w-4 text-gray-400 hover:text-white"})})]})})},i=e=>{let{toasts:s,onRemove:a}=e;return(0,t.jsx)("div",{className:"fixed top-4 right-4 z-50 space-y-3 max-w-sm w-full",children:s.map(e=>(0,t.jsx)(n,{toast:e,onRemove:a},e.id))})},c=()=>{let[e,s]=(0,r.useState)([]),a=e=>{var a;let t=Math.random().toString(36).substr(2,9),r={...e,id:t,duration:null!=(a=e.duration)?a:5e3};return s(e=>[...e,r]),t};return{toasts:e,addToast:a,removeToast:e=>{s(s=>s.filter(s=>s.id!==e))},success:(e,s,t)=>a({type:"success",title:e,message:s,duration:t}),error:(e,s,t)=>a({type:"error",title:e,message:s,duration:t}),warning:(e,s,t)=>a({type:"warning",title:e,message:s,duration:t}),info:(e,s,t)=>a({type:"info",title:e,message:s,duration:t})}}},69903:(e,s,a)=>{a.d(s,{A:()=>u});var t=a(95155),r=a(12115),l=a(35695),n=a(99323);let i=()=>(0,t.jsxs)("div",{className:"space-y-6 animate-pulse",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/3"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[1,2,3].map(e=>(0,t.jsxs)("div",{className:"bg-white p-6 rounded-lg border",children:[(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2 mb-4"}),(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4"})]},e))}),(0,t.jsxs)("div",{className:"bg-white p-6 rounded-lg border",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/4 mb-4"}),(0,t.jsx)("div",{className:"space-y-3",children:[1,2,3,4].map(e=>(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded"},e))})]})]}),c=()=>(0,t.jsxs)("div",{className:"space-y-8 animate-pulse",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"h-12 bg-gray-200 rounded w-1/2 mx-auto mb-4"}),(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-3/4 mx-auto"})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[1,2,3].map(e=>(0,t.jsxs)("div",{className:"bg-white p-8 rounded-2xl border-2",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/2 mx-auto mb-2"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mx-auto mb-4"}),(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded w-1/3 mx-auto"})]}),(0,t.jsx)("div",{className:"space-y-3 mb-8",children:[1,2,3,4,5].map(e=>(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded"},e))}),(0,t.jsx)("div",{className:"h-12 bg-gray-200 rounded"})]},e))})]}),d=()=>(0,t.jsxs)("div",{className:"space-y-8 animate-pulse",children:[(0,t.jsxs)("div",{className:"text-center py-20 bg-gradient-to-br from-slate-50 to-blue-50",children:[(0,t.jsx)("div",{className:"h-16 bg-gray-200 rounded w-2/3 mx-auto mb-6"}),(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-3/4 mx-auto"})]}),(0,t.jsx)("div",{className:"py-20",children:(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-12",children:[1,2,3,4,5,6].map(e=>(0,t.jsx)("div",{className:"bg-white rounded-2xl p-8 border",children:(0,t.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-gray-200 rounded-xl"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/2 mb-3"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded mb-4"}),(0,t.jsx)("div",{className:"space-y-2",children:[1,2,3].map(e=>(0,t.jsx)("div",{className:"h-3 bg-gray-200 rounded w-3/4"},e))})]})]})},e))})})]}),o=()=>(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-blue-50",children:(0,t.jsxs)("div",{className:"bg-white p-8 rounded-2xl shadow-lg border w-full max-w-md animate-pulse",children:[(0,t.jsxs)("div",{className:"text-center mb-8",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/2 mx-auto mb-2"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mx-auto"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("div",{className:"h-12 bg-gray-200 rounded"}),(0,t.jsx)("div",{className:"h-12 bg-gray-200 rounded"}),(0,t.jsx)("div",{className:"h-12 bg-gray-200 rounded"}),(0,t.jsx)("div",{className:"h-12 bg-gray-200 rounded"})]})]})}),m=()=>(0,t.jsxs)("div",{className:"space-y-6 animate-pulse",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/4"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"bg-white p-6 rounded-lg border",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/3 mb-4"}),(0,t.jsx)("div",{className:"space-y-3",children:[1,2,3].map(e=>(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded"},e))}),(0,t.jsx)("div",{className:"h-32 bg-gray-200 rounded mt-4"}),(0,t.jsx)("div",{className:"h-10 bg-gray-200 rounded mt-4"})]}),(0,t.jsxs)("div",{className:"bg-white p-6 rounded-lg border",children:[(0,t.jsx)("div",{className:"h-6 bg-gray-200 rounded w-1/3 mb-4"}),(0,t.jsx)("div",{className:"h-64 bg-gray-200 rounded"})]})]})]}),x=()=>(0,t.jsxs)("div",{className:"space-y-6 animate-pulse",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded w-1/3"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-2/3"}),(0,t.jsx)("div",{className:"bg-white p-6 rounded-lg border",children:(0,t.jsx)("div",{className:"space-y-4",children:[1,2,3,4,5].map(e=>(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded"},e))})})]});function u(e){let s,{targetRoute:a,children:u}=e,[g,h]=(0,r.useState)(!0),[p,y]=(0,r.useState)(!1),f=(0,l.usePathname)(),b=(0,r.useRef)(),{isPageCached:j}=(0,n.bu)()||{isPageCached:()=>!1};return((0,r.useEffect)(()=>(f===a&&(b.current=setTimeout(()=>{y(!0),setTimeout(()=>h(!1),100)},j(a)?50:200)),()=>{b.current&&clearTimeout(b.current)}),[f,a,j]),(0,r.useEffect)(()=>{h(!0),y(!1)},[a]),f!==a&&g||f===a&&g&&!p)?(0,t.jsx)("div",{className:"optimistic-loading-container",children:(s=a).startsWith("/dashboard")?(0,t.jsx)(i,{}):s.startsWith("/pricing")?(0,t.jsx)(c,{}):s.startsWith("/features")?(0,t.jsx)(d,{}):s.startsWith("/auth/")?(0,t.jsx)(o,{}):s.startsWith("/playground")?(0,t.jsx)(m,{}):(0,t.jsx)(x,{})}):(0,t.jsx)("div",{className:"transition-opacity duration-300 ".concat(p?"opacity-100":"opacity-0"),children:u})}},71848:(e,s,a)=>{a.d(s,{A:()=>i});var t=a(95155),r=a(23405);let l=e=>{let{label:s,value:a}=e;return(0,t.jsxs)("div",{className:"py-2 sm:grid sm:grid-cols-3 sm:gap-4",children:[(0,t.jsx)("dt",{className:"text-sm font-medium text-gray-700",children:s}),(0,t.jsx)("dd",{className:"mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2 break-words",children:null!=a&&""!==a?a:"N/A"})]})},n=e=>{let s,{title:a,data:r}=e;if(null==r)s="N/A";else if("string"==typeof r)s=r;else try{s=JSON.stringify(r,null,2)}catch(e){s="Invalid JSON data"}return(0,t.jsxs)("div",{className:"py-2",children:[(0,t.jsx)("dt",{className:"text-sm font-medium text-gray-700 mb-1",children:a}),(0,t.jsx)("dd",{className:"mt-1 text-sm text-gray-900 bg-gray-50 p-3 rounded-lg border",children:(0,t.jsx)("pre",{className:"whitespace-pre-wrap break-all",children:s})})]})};function i(e){var s;let{log:a,onClose:i,apiConfigNameMap:c}=e;if(!a)return null;let d=a.custom_api_config_id?c[a.custom_api_config_id]||"Unknown Model":"N/A";return(0,t.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50 transition-opacity duration-300 ease-in-out",onClick:i,children:(0,t.jsxs)("div",{className:"card max-w-2xl w-full max-h-[90vh] flex flex-col",onClick:e=>e.stopPropagation(),children:[(0,t.jsxs)("div",{className:"flex justify-between items-center p-6 border-b border-gray-200",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-gray-900",children:["Log Details (ID: ",a.id.substring(0,8),"...)"]}),(0,t.jsx)("button",{onClick:i,className:"text-gray-500 hover:text-gray-700 hover:bg-gray-100 p-1 rounded-lg transition-colors duration-200",children:(0,t.jsx)(r.f,{className:"h-6 w-6"})})]}),(0,t.jsxs)("div",{className:"p-6 space-y-4 overflow-y-auto",children:[(0,t.jsxs)("dl",{className:"divide-y divide-gray-200",children:[(0,t.jsx)(l,{label:"Timestamp",value:new Date(a.request_timestamp).toLocaleString()}),(0,t.jsx)(l,{label:"API Model Used",value:d}),(0,t.jsx)(l,{label:"Role Requested",value:a.role_requested}),(0,t.jsx)(l,{label:"Role Used",value:a.role_used}),(0,t.jsx)(l,{label:"Status",value:null===(s=a.status_code)?(0,t.jsx)("span",{className:"px-2 py-0.5 rounded-full text-xs bg-gray-600 text-gray-100",children:"N/A"}):s>=200&&s<300?(0,t.jsxs)("span",{className:"px-2 py-0.5 rounded-full text-xs bg-green-600 text-green-100",children:["Success (",s,")"]}):s>=400?(0,t.jsxs)("span",{className:"px-2 py-0.5 rounded-full text-xs bg-red-600 text-red-100",children:["Error (",s,")"]}):(0,t.jsxs)("span",{className:"px-2 py-0.5 rounded-full text-xs bg-yellow-500 text-yellow-100",children:["Other (",s,")"]})}),(0,t.jsx)(l,{label:"LLM Provider",value:a.llm_provider_name}),(0,t.jsx)(l,{label:"LLM Model Name",value:(()=>{var e,s;if((null==(e=a.role_used)?void 0:e.includes("RouKey_Multi Roles_"))&&(null==(s=a.response_payload_summary)?void 0:s.models_used)){let e=a.response_payload_summary.models_used;return e.length<=3?e.join(", "):"".concat(e.slice(0,3).join(", "),"...")}return a.llm_model_name})()}),(0,t.jsx)(l,{label:"LLM Latency",value:null!==a.llm_provider_latency_ms?"".concat(a.llm_provider_latency_ms," ms"):"N/A"}),(0,t.jsx)(l,{label:"RoKey Latency",value:null!==a.processing_duration_ms?"".concat(a.processing_duration_ms," ms"):"N/A"}),(0,t.jsx)(l,{label:"Input Tokens",value:null!==a.input_tokens?a.input_tokens:"N/A"}),(0,t.jsx)(l,{label:"Output Tokens",value:null!==a.output_tokens?a.output_tokens:"N/A"}),(0,t.jsx)(l,{label:"Cost",value:null!==a.cost?"$".concat(a.cost.toFixed(6)):"N/A"}),(0,t.jsx)(l,{label:"Multimodal Request",value:a.is_multimodal?"Yes":"No"}),(0,t.jsx)(l,{label:"IP Address",value:a.ip_address}),a.user_id&&(0,t.jsx)(l,{label:"User ID",value:a.user_id}),a.error_message&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(l,{label:"Error Message",value:a.error_message}),(0,t.jsx)(l,{label:"Error Source",value:a.error_source})]}),a.llm_provider_status_code&&(0,t.jsx)(l,{label:"LLM Provider Status",value:a.llm_provider_status_code})]}),a.request_payload_summary&&(0,t.jsx)(n,{title:"Request Payload Summary",data:a.request_payload_summary}),a.response_payload_summary&&(0,t.jsx)(n,{title:"Response Payload Summary",data:a.response_payload_summary}),a.error_details_zod&&(0,t.jsx)(n,{title:"Zod Validation Error Details",data:a.error_details_zod})]}),(0,t.jsx)("div",{className:"p-6 border-t border-gray-200 text-right",children:(0,t.jsx)("button",{onClick:i,className:"btn-secondary",children:"Close"})})]})})}},74338:(e,s,a)=>{a.d(s,{Ay:()=>r,B0:()=>l,F6:()=>n});var t=a(95155);function r(e){let{size:s="md",className:a=""}=e;return(0,t.jsx)("div",{className:"animate-spin rounded-full border-2 border-gray-600 border-t-indigo-500 ".concat({sm:"w-4 h-4",md:"w-6 h-6",lg:"w-8 h-8"}[s]," ").concat(a)})}function l(e){let{className:s=""}=e;return(0,t.jsx)("div",{className:"glass rounded-2xl p-6 animate-pulse ".concat(s),children:(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-gray-700 rounded-xl"}),(0,t.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,t.jsx)("div",{className:"h-4 bg-gray-700 rounded w-3/4"}),(0,t.jsx)("div",{className:"h-3 bg-gray-700 rounded w-1/2"})]})]})})}function n(e){let{rows:s=5,columns:a=4}=e;return(0,t.jsx)("div",{className:"glass rounded-2xl overflow-hidden",children:(0,t.jsxs)("div",{className:"animate-pulse",children:[(0,t.jsx)("div",{className:"bg-gray-800 p-4 border-b border-gray-700",children:(0,t.jsx)("div",{className:"grid gap-4",style:{gridTemplateColumns:"repeat(".concat(a,", 1fr)")},children:Array.from({length:a}).map((e,s)=>(0,t.jsx)("div",{className:"h-4 bg-gray-700 rounded"},s))})}),Array.from({length:s}).map((e,s)=>(0,t.jsx)("div",{className:"p-4 border-b border-gray-700 last:border-b-0",children:(0,t.jsx)("div",{className:"grid gap-4",style:{gridTemplateColumns:"repeat(".concat(a,", 1fr)")},children:Array.from({length:a}).map((e,s)=>(0,t.jsx)("div",{className:"h-4 bg-gray-700 rounded"},s))})},s))]})})}},76346:(e,s,a)=>{a.d(s,{z:()=>D});var t=a(95155),r=a(12115),l=a(13741);let n=(0,r.forwardRef)((e,s)=>{let{className:a="",variant:r="default",hover:l=!1,padding:n="md",children:i,...c}=e;return(0,t.jsx)("div",{ref:s,className:"".concat("rounded-xl transition-all duration-200"," ").concat({default:"card",glass:"glass",gradient:"gradient-surface border border-white/10"}[r]," ").concat({sm:"p-4",md:"p-6",lg:"p-8"}[n]," ").concat(l?"hover:shadow-md hover:-translate-y-1 cursor-pointer":""," ").concat(a),...c,children:i})});n.displayName="Card";let i=e=>{let{className:s="",title:a,subtitle:r,action:l,children:n,...i}=e;return(0,t.jsxs)("div",{className:"flex items-center justify-between mb-6 ".concat(s),...i,children:[(0,t.jsxs)("div",{children:[a&&(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:a}),r&&(0,t.jsx)("p",{className:"text-gray-600 dark:text-gray-400 text-sm mt-1",children:r}),n]}),l&&(0,t.jsx)("div",{children:l})]})},c=e=>{let{className:s="",children:a,...r}=e;return(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white ".concat(s),...r,children:a})},d=e=>{let{className:s="",children:a,...r}=e;return(0,t.jsx)("div",{className:"".concat(s),...r,children:a})};var o=a(75898),m=a(56671),x=a(26126),u=a(87266),g=a(574);function h(e){let{apiKey:s,onRevoke:a}=e,r=async e=>{try{await navigator.clipboard.writeText(e),m.oR.success("API key copied to clipboard")}catch(e){m.oR.error("Failed to copy API key")}},o=s.expires_at&&new Date(s.expires_at)<new Date,h="active"===s.status&&!o;return(0,t.jsxs)(n,{className:"transition-all duration-200 hover:shadow-md ".concat(h?"":"opacity-75"),children:[(0,t.jsx)(i,{className:"pb-3",children:(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)(c,{className:"text-lg font-semibold",children:s.key_name}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["Configuration: ",s.custom_api_configs.name]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(x.E,{className:(e=>{switch(e){case"active":return"bg-green-100 text-green-800 border-green-200";case"inactive":return"bg-yellow-100 text-yellow-800 border-yellow-200";case"revoked":return"bg-red-100 text-red-800 border-red-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}})(s.status),children:s.status}),o&&(0,t.jsx)(x.E,{className:"bg-red-100 text-red-800 border-red-200",children:"Expired"})]})]})}),(0,t.jsxs)(d,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"API Key (Masked)"}),(0,t.jsxs)("div",{className:"flex items-center gap-2 p-3 bg-gray-50 rounded-lg border",children:[(0,t.jsxs)("code",{className:"flex-1 text-sm font-mono text-gray-600",children:[s.key_prefix,"_","*".repeat(28),"string"==typeof s.masked_key?s.masked_key.slice(-4):"xxxx"]}),(0,t.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>r("".concat(s.key_prefix,"_").concat("*".repeat(28)).concat("string"==typeof s.masked_key?s.masked_key.slice(-4):"xxxx")),className:"h-8 w-8 p-0",title:"Copy masked key (for reference only)",children:(0,t.jsx)(u.QR,{className:"h-4 w-4"})})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2 text-xs text-amber-600 bg-amber-50 p-2 rounded",children:[(0,t.jsx)("span",{children:"⚠️"}),(0,t.jsx)("span",{children:"Full API key was only shown once during creation for security. Save it securely when creating new keys."})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Permissions"}),(0,t.jsxs)("div",{className:"flex flex-wrap gap-2",children:[s.permissions.chat&&(0,t.jsx)(x.E,{variant:"secondary",className:"text-xs",children:"Chat Completions"}),s.permissions.streaming&&(0,t.jsx)(x.E,{variant:"secondary",className:"text-xs",children:"Streaming"}),s.permissions.all_models&&(0,t.jsx)(x.E,{variant:"secondary",className:"text-xs",children:"All Models"})]})]}),(s.allowed_ips.length>0||s.allowed_domains.length>0)&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("label",{className:"text-sm font-medium text-gray-700 flex items-center gap-1",children:[(0,t.jsx)(u.ek,{className:"h-4 w-4"}),"Security Restrictions"]}),(0,t.jsxs)("div",{className:"space-y-1 text-xs",children:[s.allowed_ips.length>0&&(0,t.jsxs)("div",{className:"flex items-center gap-1 text-gray-600",children:[(0,t.jsx)("span",{className:"font-medium",children:"IPs:"}),(0,t.jsx)("span",{children:s.allowed_ips.join(", ")})]}),s.allowed_domains.length>0&&(0,t.jsxs)("div",{className:"flex items-center gap-1 text-gray-600",children:[(0,t.jsx)(u.qz,{className:"h-3 w-3"}),(0,t.jsx)("span",{className:"font-medium",children:"Domains:"}),(0,t.jsx)("span",{children:s.allowed_domains.join(", ")})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("label",{className:"text-sm font-medium text-gray-700 flex items-center gap-1",children:[(0,t.jsx)(u.Il,{className:"h-4 w-4"}),"Usage Statistics"]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Total Requests:"}),(0,t.jsx)("span",{className:"ml-2 font-semibold",children:s.total_requests.toLocaleString()})]}),s.last_used_at&&(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Last Used:"}),(0,t.jsx)("span",{className:"ml-2 font-semibold",children:(0,g.m)(new Date(s.last_used_at),{addSuffix:!0})})]})]})]}),s.expires_at&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("label",{className:"text-sm font-medium text-gray-700 flex items-center gap-1",children:[(0,t.jsx)(u.Vv,{className:"h-4 w-4"}),"Expiration"]}),(0,t.jsxs)("div",{className:"text-sm",children:[(0,t.jsxs)("span",{className:"font-semibold ".concat(o?"text-red-600":"text-gray-900"),children:[new Date(s.expires_at).toLocaleDateString()," at"," ",new Date(s.expires_at).toLocaleTimeString()]}),!o&&(0,t.jsxs)("span",{className:"ml-2 text-gray-600",children:["(",(0,g.m)(new Date(s.expires_at),{addSuffix:!0}),")"]})]})]}),(0,t.jsx)("div",{className:"flex items-center justify-end pt-2 border-t",children:"revoked"!==s.status&&(0,t.jsxs)(l.$,{variant:"destructive",size:"sm",onClick:()=>a(s.id),className:"text-xs",children:[(0,t.jsx)(u.TB,{className:"h-3 w-3 mr-1"}),"Revoke"]})})]})]})}var p=a(47477),y=a(76288);let f=p.bL;p.l9;let b=p.ZL;p.bm;let j=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(p.hJ,{ref:s,className:"fixed inset-0 z-50 bg-black/50 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 ".concat(a||""),...r})});j.displayName=p.hJ.displayName;let v=r.forwardRef((e,s)=>{let{className:a,children:r,...l}=e;return(0,t.jsxs)(b,{children:[(0,t.jsx)(j,{}),(0,t.jsxs)(p.UC,{ref:s,className:"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-white p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg ".concat(a||""),...l,children:[r,(0,t.jsxs)(p.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,t.jsx)(y.X,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});v.displayName=p.UC.displayName;let N=e=>{let{className:s,...a}=e;return(0,t.jsx)("div",{className:"flex flex-col space-y-1.5 text-center sm:text-left ".concat(s||""),...a})};N.displayName="DialogHeader";let w=e=>{let{className:s,...a}=e;return(0,t.jsx)("div",{className:"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 ".concat(s||""),...a})};w.displayName="DialogFooter";let k=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(p.hE,{ref:s,className:"text-lg font-semibold leading-none tracking-tight ".concat(a||""),...r})});k.displayName=p.hE.displayName;let _=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(p.VY,{ref:s,className:"text-sm text-gray-600 ".concat(a||""),...r})});_.displayName=p.VY.displayName;let C=(0,r.forwardRef)((e,s)=>{let{className:a="",label:r,error:l,helperText:n,icon:i,iconPosition:c="left",id:d,...o}=e,m=d||"input-".concat(Math.random().toString(36).substr(2,9));return(0,t.jsxs)("div",{className:"space-y-2",children:[r&&(0,t.jsx)("label",{htmlFor:m,className:"block text-sm font-medium text-gray-300",children:r}),(0,t.jsxs)("div",{className:"relative",children:[i&&"left"===c&&(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)("div",{className:"h-5 w-5 text-gray-400",children:i})}),(0,t.jsx)("input",{ref:s,id:m,className:"\n              w-full p-3 bg-white/5 border rounded-xl text-white placeholder-gray-400 \n              focus:ring-2 focus:ring-indigo-500 focus:border-transparent \n              disabled:opacity-50 disabled:cursor-not-allowed\n              transition-all duration-200\n              ".concat(l?"border-red-500 focus:ring-red-500":"border-gray-600","\n              ").concat(i&&"left"===c?"pl-10":"","\n              ").concat(i&&"right"===c?"pr-10":"","\n              ").concat(a,"\n            "),...o}),i&&"right"===c&&(0,t.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:(0,t.jsx)("div",{className:"h-5 w-5 text-gray-400",children:i})})]}),l&&(0,t.jsx)("p",{className:"text-sm text-red-400",children:l}),n&&!l&&(0,t.jsx)("p",{className:"text-sm text-gray-500",children:n})]})});C.displayName="Input",(0,r.forwardRef)((e,s)=>{let{className:a="",label:r,error:l,helperText:n,id:i,...c}=e,d=i||"textarea-".concat(Math.random().toString(36).substr(2,9));return(0,t.jsxs)("div",{className:"space-y-2",children:[r&&(0,t.jsx)("label",{htmlFor:d,className:"block text-sm font-medium text-gray-300",children:r}),(0,t.jsx)("textarea",{ref:s,id:d,className:"\n            w-full p-3 bg-white/5 border rounded-xl text-white placeholder-gray-400 \n            focus:ring-2 focus:ring-indigo-500 focus:border-transparent \n            disabled:opacity-50 disabled:cursor-not-allowed\n            transition-all duration-200 resize-none\n            ".concat(l?"border-red-500 focus:ring-red-500":"border-gray-600","\n            ").concat(a,"\n          "),...c}),l&&(0,t.jsx)("p",{className:"text-sm text-red-400",children:l}),n&&!l&&(0,t.jsx)("p",{className:"text-sm text-gray-500",children:n})]})}).displayName="Textarea",(0,r.forwardRef)((e,s)=>{let{className:a="",label:r,error:l,helperText:n,options:i=[],children:c,id:d,...o}=e,m=d||"select-".concat(Math.random().toString(36).substr(2,9));return(0,t.jsxs)("div",{className:"space-y-2",children:[r&&(0,t.jsx)("label",{htmlFor:m,className:"block text-sm font-medium text-gray-300",children:r}),(0,t.jsxs)("select",{ref:s,id:m,className:"\n            w-full p-3 bg-white/5 border rounded-xl text-white \n            focus:ring-2 focus:ring-indigo-500 focus:border-transparent \n            disabled:opacity-50 disabled:cursor-not-allowed\n            transition-all duration-200\n            ".concat(l?"border-red-500 focus:ring-red-500":"border-gray-600","\n            ").concat(a,"\n          "),...o,children:[i.map(e=>(0,t.jsx)("option",{value:e.value,disabled:e.disabled,className:"bg-gray-800 text-white",children:e.label},e.value)),c]}),l&&(0,t.jsx)("p",{className:"text-sm text-red-400",children:l}),n&&!l&&(0,t.jsx)("p",{className:"text-sm text-gray-500",children:n})]})}).displayName="Select";var A=a(85057);let P=(0,a(74466).F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-blue-50 text-blue-900 border-blue-200",destructive:"bg-red-50 text-red-900 border-red-200 [&>svg]:text-red-600"}},defaultVariants:{variant:"default"}}),S=r.forwardRef((e,s)=>{let{className:a,variant:r,...l}=e;return(0,t.jsx)("div",{ref:s,role:"alert",className:"".concat(P({variant:r})," ").concat(a||""),...l})});S.displayName="Alert",r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("h5",{ref:s,className:"mb-1 font-medium leading-none tracking-tight ".concat(a||""),...r})}).displayName="AlertTitle";let R=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("div",{ref:s,className:"text-sm [&_p]:leading-relaxed ".concat(a||""),...r})});R.displayName="AlertDescription";var E=a(31547);function L(e){let{open:s,onOpenChange:a,onCreateApiKey:n,configName:i,creating:c,subscriptionTier:d}=e,[o,x]=(0,r.useState)("form"),[u,g]=(0,r.useState)(null),[h,p]=(0,r.useState)(!0),[y,b]=(0,r.useState)(!1),[j,P]=(0,r.useState)({key_name:"",expires_at:""}),L=async e=>{if(e.preventDefault(),!j.key_name.trim())return void m.oR.error("Please enter a name for your API key");try{let e=await n({key_name:j.key_name.trim(),expires_at:j.expires_at||void 0});g(e),x("success")}catch(e){}},I=async()=>{if(null==u?void 0:u.api_key)try{await navigator.clipboard.writeText(u.api_key),b(!0),m.oR.success("API key copied to clipboard"),setTimeout(()=>b(!1),2e3)}catch(e){m.oR.error("Failed to copy API key")}},T=()=>{"form"===o&&(x("form"),g(null),p(!0),P({key_name:"",expires_at:""}),a(!1))};return"success"===o&&u?(0,t.jsx)(f,{open:s,onOpenChange:()=>{},modal:!0,children:(0,t.jsxs)(v,{className:"max-w-lg",children:[(0,t.jsxs)(N,{className:"text-center space-y-3",children:[(0,t.jsx)("div",{className:"mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center",children:(0,t.jsx)(E.Uz,{className:"h-8 w-8 text-green-600"})}),(0,t.jsx)(k,{className:"text-2xl font-bold text-gray-900",children:"API Key Created Successfully!"}),(0,t.jsx)(_,{className:"text-gray-600",children:"Save your API key now - this is the only time you'll see it in full."})]}),(0,t.jsxs)("div",{className:"space-y-6 py-4",children:[(0,t.jsxs)(S,{className:"border-red-200 bg-red-50",children:[(0,t.jsx)(E.hc,{className:"h-4 w-4 text-red-600"}),(0,t.jsxs)(R,{className:"text-red-800 font-medium",children:[(0,t.jsx)("strong",{children:"Important:"})," This is the only time you'll see the full API key. Make sure to copy and store it securely."]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(A.J,{className:"text-sm font-medium text-gray-700",children:"Your API Key"}),(0,t.jsx)("div",{className:"relative",children:(0,t.jsxs)("div",{className:"flex items-center gap-2 p-3 bg-gray-50 rounded-lg border border-gray-200",children:[(0,t.jsx)("code",{className:"flex-1 text-sm font-mono text-gray-900 break-all select-all",children:h?u.api_key:"".concat(u.key_prefix,"_").concat("*".repeat(28)).concat(u.api_key.slice(-4))}),(0,t.jsxs)("div",{className:"flex gap-1",children:[(0,t.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>p(!h),className:"h-8 w-8 p-0",title:h?"Hide key":"Show key",children:h?(0,t.jsx)(E.X_,{className:"h-4 w-4"}):(0,t.jsx)(E.kU,{className:"h-4 w-4"})}),(0,t.jsx)(l.$,{variant:"ghost",size:"sm",onClick:I,className:"h-8 w-8 p-0 ".concat(y?"text-green-600":""),title:"Copy to clipboard",children:y?(0,t.jsx)("span",{className:"text-xs",children:"✓"}):(0,t.jsx)(E.QR,{className:"h-4 w-4"})})]})]})}),(0,t.jsx)(l.$,{onClick:I,variant:"outline",className:"w-full",disabled:y,children:y?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("span",{className:"text-green-600 mr-2",children:"✓"}),"Copied!"]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(E.QR,{className:"h-4 w-4 mr-2"}),"Copy API Key"]})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm bg-gray-50 p-4 rounded-lg",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(A.J,{className:"text-gray-600",children:"Key Name"}),(0,t.jsx)("p",{className:"font-medium text-gray-900",children:u.key_name})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(A.J,{className:"text-gray-600",children:"Created"}),(0,t.jsx)("p",{className:"font-medium text-gray-900",children:new Date(u.created_at).toLocaleString()})]})]})]}),(0,t.jsx)(w,{className:"pt-6",children:(0,t.jsx)(l.$,{onClick:()=>{x("form"),g(null),p(!0),b(!1),P({key_name:"",expires_at:""}),a(!1)},className:"w-full",children:"I've Saved My API Key"})})]})}):(0,t.jsx)(f,{open:s,onOpenChange:T,children:(0,t.jsxs)(v,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)(N,{children:[(0,t.jsxs)(k,{className:"flex items-center gap-2",children:[(0,t.jsx)(E.Uz,{className:"h-5 w-5"}),"Create API Key"]}),(0,t.jsxs)(_,{children:["Create a new API key for programmatic access to ",i]})]}),(0,t.jsxs)("form",{onSubmit:L,className:"space-y-6",children:[(0,t.jsx)("div",{className:"space-y-4",children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(A.J,{htmlFor:"key_name",children:"API Key Name *"}),(0,t.jsx)(C,{id:"key_name",value:j.key_name,onChange:e=>P(s=>({...s,key_name:e.target.value})),placeholder:"e.g., Production API Key",required:!0,className:"!text-gray-900 !bg-white !border-gray-300 !placeholder-gray-500"}),(0,t.jsx)("p",{className:"text-xs text-gray-600",children:"A descriptive name to help you identify this API key"})]})}),(0,t.jsx)("div",{className:"space-y-4",children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(A.J,{htmlFor:"expires_at",children:"Expiration Date (Optional)"}),(0,t.jsx)(C,{id:"expires_at",type:"datetime-local",value:j.expires_at,onChange:e=>P(s=>({...s,expires_at:e.target.value})),min:new Date().toISOString().slice(0,16),className:"!text-gray-900 !bg-white !border-gray-300 !placeholder-gray-500"}),(0,t.jsx)("p",{className:"text-xs text-gray-600",children:"Leave empty for no expiration"})]})}),(0,t.jsxs)(w,{children:[(0,t.jsx)(l.$,{type:"button",variant:"outline",onClick:T,children:"Cancel"}),(0,t.jsx)(l.$,{type:"submit",disabled:c,children:c?"Creating...":"Create API Key"})]})]})]})})}var I=a(60993),T=a(80377),z=a(87162);function D(e){let{configId:s,configName:a}=e,[i,c]=(0,r.useState)([]),[x,u]=(0,r.useState)(!0),[g,p]=(0,r.useState)(!1),[y,f]=(0,r.useState)(!1),[b,j]=(0,r.useState)(null),v=(0,z.Z)(),N=async()=>{try{u(!0);let e=await fetch("/api/user-api-keys?config_id=".concat(s));if(!e.ok)throw Error("Failed to fetch API keys");let a=await e.json();c(a.api_keys||[])}catch(e){m.oR.error("Failed to load API keys")}finally{u(!1)}},w=async e=>{try{let s=await fetch("/api/user/subscription-tier"),a=s.ok?await s.json():null,t=(null==a?void 0:a.tier)||"starter",r={free:3,starter:50,professional:999999,enterprise:999999},l=void 0!==e?e:i.length;j({tier:t,keyLimit:r[t]||r.free,currentCount:l})}catch(s){j({tier:"free",keyLimit:3,currentCount:void 0!==e?e:i.length})}};(0,r.useEffect)(()=>{N()},[s]),(0,r.useEffect)(()=>{i.length>=0&&w()},[i]);let k=async e=>{try{p(!0);let t=await fetch("/api/user-api-keys",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...e,custom_api_config_id:s})});if(!t.ok){let e=await t.json();throw Error(e.error||"Failed to create API key")}let r=await t.json();m.oR.success("API key created successfully!");let l={...r,custom_api_configs:{id:s,name:a}};return c(e=>{let s=[l,...e];return w(s.length),s}),await N(),r}catch(e){throw m.oR.error(e.message||"Failed to create API key"),e}finally{p(!1)}},_=async e=>{let s=i.find(s=>s.id===e),a=(null==s?void 0:s.key_name)||"this API key";v.showConfirmation({title:"Revoke API Key",message:'Are you sure you want to revoke "'.concat(a,'"? This action cannot be undone and will immediately disable the key.'),confirmText:"Revoke Key",cancelText:"Cancel",type:"danger"},async()=>{try{let s=await fetch("/api/user-api-keys/".concat(e),{method:"DELETE"});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to revoke API key")}c(s=>s.map(s=>s.id===e?{...s,status:"revoked"}:s)),m.oR.success("API key revoked successfully")}catch(e){throw m.oR.error(e.message||"Failed to revoke API key"),e}})},C=!b||b.currentCount<b.keyLimit;return x?(0,t.jsx)(n,{children:(0,t.jsxs)(d,{className:"flex items-center justify-center py-8",children:[(0,t.jsx)(o.e9,{className:"h-6 w-6 animate-spin mr-2"}),"Loading API keys..."]})}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h2",{className:"text-2xl font-bold flex items-center gap-2",children:[(0,t.jsx)(o.Uz,{className:"h-6 w-6"}),"API Keys"]}),(0,t.jsxs)("p",{className:"text-gray-600 mt-1",children:["Generate API keys for programmatic access to ",a]})]}),C?(0,t.jsxs)(l.$,{onClick:()=>f(!0),className:"flex items-center gap-2",children:[(0,t.jsx)(o.FW,{className:"h-4 w-4"}),"Create API Key"]}):(0,t.jsxs)("div",{className:"flex flex-col items-end gap-2",children:[(0,t.jsxs)(l.$,{disabled:!0,className:"flex items-center gap-2 opacity-50",children:[(0,t.jsx)(o.FW,{className:"h-4 w-4"}),"Create API Key"]}),(0,t.jsx)("p",{className:"text-xs text-orange-600 font-medium",children:(null==b?void 0:b.tier)==="free"?"Upgrade to Starter plan for more API keys":"API key limit reached - upgrade for unlimited keys"})]})]}),b&&(0,t.jsx)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg px-4 py-2 mb-4",children:(0,t.jsx)(I.Jg,{current:b.currentCount,limit:b.keyLimit,label:"User-Generated API Keys",tier:b.tier,showUpgradeHint:!0})}),0===i.length?(0,t.jsx)(n,{children:(0,t.jsxs)(d,{className:"text-center py-8",children:[(0,t.jsx)(o.Uz,{className:"h-12 w-12 mx-auto text-gray-400 mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"No API Keys"}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"Create your first API key to start using the RouKey API programmatically."}),C?(0,t.jsxs)(l.$,{onClick:()=>f(!0),children:[(0,t.jsx)(o.FW,{className:"h-4 w-4 mr-2"}),"Create Your First API Key"]}):(0,t.jsxs)("div",{className:"flex flex-col items-center gap-2",children:[(0,t.jsxs)(l.$,{disabled:!0,className:"opacity-50",children:[(0,t.jsx)(o.FW,{className:"h-4 w-4 mr-2"}),"Create Your First API Key"]}),(0,t.jsx)("p",{className:"text-xs text-orange-600 font-medium",children:(null==b?void 0:b.tier)==="free"?"Upgrade to Starter plan to create API keys":"API key limit reached - upgrade for unlimited keys"})]})]})}):(0,t.jsx)("div",{className:"grid gap-4",children:i.map(e=>(0,t.jsx)(h,{apiKey:e,onRevoke:_},e.id))}),(0,t.jsx)(L,{open:y,onOpenChange:e=>{f(e)},onCreateApiKey:k,configName:a,creating:g,subscriptionTier:(null==b?void 0:b.tier)||"starter"}),(0,t.jsx)(T.A,{isOpen:v.isOpen,onClose:v.hideConfirmation,onConfirm:v.onConfirm,title:v.title,message:v.message,confirmText:v.confirmText,cancelText:v.cancelText,type:v.type,isLoading:v.isLoading})]})}},78817:(e,s,a)=>{a.d(s,{A:()=>c});var t=a(95155);a(12115);var r=a(89732),l=a(99323),n=a(95565);let i={"/dashboard":{title:"Dashboard",subtitle:"Loading overview & analytics...",icon:r.fA,color:"text-blue-500",bgColor:"bg-blue-50"},"/my-models":{title:"My Models",subtitle:"Loading API key management...",icon:r.RY,color:"text-green-500",bgColor:"bg-green-50"},"/playground":{title:"Playground",subtitle:"Loading model testing environment...",icon:r.cu,color:"text-orange-500",bgColor:"bg-orange-50"},"/routing-setup":{title:"Routing Setup",subtitle:"Loading routing configuration...",icon:r.sR,color:"text-purple-500",bgColor:"bg-purple-50"},"/logs":{title:"Logs",subtitle:"Loading request history...",icon:r.AQ,color:"text-gray-500",bgColor:"bg-gray-50"},"/training":{title:"Prompt Engineering",subtitle:"Loading custom prompts...",icon:r.tl,color:"text-indigo-500",bgColor:"bg-indigo-50"},"/analytics":{title:"Analytics",subtitle:"Loading advanced insights...",icon:r.r9,color:"text-pink-500",bgColor:"bg-pink-50"}};function c(e){let{targetRoute:s}=e,{clearNavigation:a}=(0,l.bu)()||{clearNavigation:()=>{}};if(!(s?i[s]:null))return(0,t.jsx)("div",{className:"flex items-center justify-center min-h-[60vh]",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-4 animate-pulse",children:(0,t.jsx)(r.cu,{className:"w-8 h-8 text-gray-400"})}),(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Loading..."}),(0,t.jsx)("p",{className:"text-gray-500",children:"Please wait while we load the page"})]})});let c=()=>(0,t.jsxs)("div",{className:"space-y-6 animate-fade-in",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded animate-pulse w-1/4"}),(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded animate-pulse w-20"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6",children:[(0,t.jsx)("div",{className:"lg:col-span-1 space-y-3",children:Array.from({length:5}).map((e,s)=>(0,t.jsx)("div",{className:"h-16 bg-gray-200 rounded-lg animate-pulse"},s))}),(0,t.jsx)("div",{className:"lg:col-span-3",children:(0,t.jsx)("div",{className:"h-96 bg-gray-200 rounded-lg animate-pulse"})})]})]}),d=()=>(0,t.jsxs)("div",{className:"space-y-6 animate-fade-in",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded animate-pulse w-1/4"}),(0,t.jsxs)("div",{className:"flex space-x-3",children:[(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded animate-pulse w-20"}),(0,t.jsx)("div",{className:"h-8 bg-gray-200 rounded animate-pulse w-16"})]})]}),(0,t.jsx)("div",{className:"space-y-3",children:Array.from({length:8}).map((e,s)=>(0,t.jsx)("div",{className:"h-12 bg-gray-200 rounded-lg animate-pulse"},s))})]});return(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("button",{onClick:a,className:"absolute top-4 right-4 z-10 p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors",title:"Cancel loading",children:(0,t.jsx)(r.fK,{className:"w-5 h-5"})}),(()=>{switch(s){case"/dashboard":default:return(0,t.jsx)(n.O2,{});case"/my-models":return(0,t.jsx)(n.MyModelsSkeleton,{});case"/playground":return(0,t.jsx)(c,{});case"/routing-setup":return(0,t.jsx)(n.RoutingSetupSkeleton,{});case"/logs":return(0,t.jsx)(d,{});case"/training":return(0,t.jsx)(n.vD,{});case"/analytics":return(0,t.jsx)(n.AnalyticsSkeleton,{})}})()]})}},80377:(e,s,a)=>{a.d(s,{A:()=>n});var t=a(95155),r=a(12115),l=a(38152);function n(e){let{isOpen:s,onClose:a,onConfirm:n,title:i,message:c,confirmText:d="Delete",cancelText:o="Cancel",type:m="danger",isLoading:x=!1}=e;(0,r.useEffect)(()=>{let e=e=>{"Escape"===e.key&&s&&!x&&a()};return s&&(document.addEventListener("keydown",e),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",e),document.body.style.overflow="unset"}},[s,x,a]);let u=(()=>{switch(m){case"danger":return{iconBg:"bg-red-100",iconColor:"text-red-600",confirmButton:"bg-red-600 hover:bg-red-700 focus:ring-red-500",icon:l.uc};case"warning":return{iconBg:"bg-yellow-100",iconColor:"text-yellow-600",confirmButton:"bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500",icon:l.Pi};default:return{iconBg:"bg-blue-100",iconColor:"text-blue-600",confirmButton:"bg-blue-600 hover:bg-blue-700 focus:ring-blue-500",icon:l.Pi}}})(),g=u.icon;return s?(0,t.jsxs)("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:[(0,t.jsx)("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm transition-opacity duration-300",onClick:x?void 0:a}),(0,t.jsx)("div",{className:"flex min-h-full items-center justify-center p-4",children:(0,t.jsxs)("div",{className:"relative w-full max-w-md transform overflow-hidden rounded-2xl bg-white shadow-2xl transition-all duration-300 scale-100 opacity-100",children:[(0,t.jsx)("div",{className:"relative px-6 pt-6",children:(0,t.jsx)("button",{onClick:a,disabled:x,className:"absolute top-4 right-4 p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed",children:(0,t.jsx)(l.fK,{className:"h-5 w-5"})})}),(0,t.jsxs)("div",{className:"px-6 pb-6",children:[(0,t.jsx)("div",{className:"mx-auto flex h-16 w-16 items-center justify-center rounded-full mb-6",children:(0,t.jsx)("div",{className:"".concat(u.iconBg," rounded-full p-3"),children:(0,t.jsx)(g,{className:"h-8 w-8 ".concat(u.iconColor)})})}),(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-900 mb-3 text-center",children:i}),(0,t.jsx)("p",{className:"text-gray-600 text-sm leading-relaxed mb-8 text-center",children:c}),(0,t.jsxs)("div",{className:"flex flex-col-reverse sm:flex-row sm:justify-center gap-3",children:[(0,t.jsx)("button",{type:"button",onClick:a,disabled:x,className:"w-full sm:w-auto px-6 py-3 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-xl hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200",children:o}),(0,t.jsx)("button",{type:"button",onClick:n,disabled:x,className:"w-full sm:w-auto px-6 py-3 text-sm font-medium text-white rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 ".concat(u.confirmButton),children:x?(0,t.jsxs)("div",{className:"flex items-center justify-center",children:[(0,t.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,t.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,t.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Processing..."]}):d})]})]})]})})]}):null}},85057:(e,s,a)=>{a.d(s,{J:()=>i});var t=a(95155),r=a(12115),l=a(40968);let n=(0,a(74466).F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),i=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(l.b,{ref:s,className:"".concat(n()," ").concat(a||""),...r})});i.displayName=l.b.displayName},95060:(e,s,a)=>{a.d(s,{A:()=>y});var t=a(95155),r=a(6874),l=a.n(r),n=a(66766),i=a(35695),c=a(12115),d=a(8652),o=a(18685),m=a(22261),x=a(99323),u=a(37843),g=a(24403),h=a(42724);let p=[{href:"/dashboard",label:"Dashboard",icon:d.fA,iconSolid:o.fA,description:"Overview & analytics"},{href:"/my-models",label:"My Models",icon:d.RY,iconSolid:o.RY,description:"API key management"},{href:"/playground",label:"Playground",icon:d.cu,iconSolid:o.cu,description:"Test your models"},{href:"/routing-setup",label:"Routing Setup",icon:d.sR,iconSolid:o.sR,description:"Configure routing"},{href:"/logs",label:"Logs",icon:d.AQ,iconSolid:o.AQ,description:"Request history"},{href:"/training",label:"Prompt Engineering",icon:d.tl,iconSolid:o.tl,description:"Custom prompts"},{href:"/analytics",label:"Analytics",icon:d.r9,iconSolid:o.r9,description:"Advanced insights"}];function y(){let e=(0,i.usePathname)(),{isCollapsed:s,isHovered:a,isHoverDisabled:r,setHovered:d}=(0,m.c)(),{navigateOptimistically:o}=(0,x.bu)()||{navigateOptimistically:()=>{}},{prefetchOnHover:y}=(0,u.C)(),{prefetchWhenIdle:f}=(0,u.e)(),{prefetchChatHistory:b}=(0,g.l2)(),{predictions:j,isLearning:v}=(0,h.x)(),N=(0,h.G)();(0,c.useEffect)(()=>{let s=p.map(e=>e.href),a=j.slice(0,2),t=N.filter(e=>"high"===e.priority).map(e=>e.route).slice(0,2);return f([...a,...t,...s.filter(s=>s!==e&&!a.includes(s)&&!t.includes(s)),"/playground","/logs"].slice(0,6))},[e,f,j,N,v]);let w=!s||a;return(0,t.jsx)("aside",{className:"sidebar flex flex-col h-full flex-shrink-0 transition-all duration-200 ease-out bg-gray-900 ".concat(w?"w-64":"w-16"),onMouseEnter:()=>!r&&d(!0),onMouseLeave:()=>!r&&d(!1),children:(0,t.jsx)("div",{className:"flex-1 relative overflow-hidden",children:(0,t.jsxs)("div",{className:"p-6 transition-all duration-200 ease-out ".concat(w?"px-6":"px-3"),children:[(0,t.jsx)("div",{className:"mb-8 pt-4 transition-all duration-200 ease-out ".concat(w?"":"text-center"),children:(0,t.jsxs)("div",{className:"relative overflow-hidden",children:[(0,t.jsx)("div",{className:"transition-all duration-200 ease-out ".concat(w?"opacity-0 scale-75 -translate-y-2":"opacity-100 scale-100 translate-y-0"," ").concat(w?"absolute":"relative"," w-8 h-8 bg-white rounded-lg flex items-center justify-center mx-auto p-0.5"),children:(0,t.jsx)(n.default,{src:"/roukey_logo.png",alt:"RouKey",width:28,height:28,className:"object-cover"})}),(0,t.jsxs)("div",{className:"transition-all duration-200 ease-out ".concat(w?"opacity-100 scale-100 translate-y-0":"opacity-0 scale-75 translate-y-2"," ").concat(w?"relative":"absolute top-0 left-0 w-full"),children:[(0,t.jsx)("h1",{className:"text-2xl font-bold text-white tracking-tight whitespace-nowrap",children:"RouKey"}),(0,t.jsx)("p",{className:"text-sm text-gray-400 mt-1 whitespace-nowrap",children:"Smart LLM Router"})]})]})}),(0,t.jsx)("nav",{className:"space-y-2",children:p.map(s=>{let a=e===s.href||e.startsWith(s.href+"/"),r=a?s.iconSolid:s.icon,n=j.includes(s.href),i=N.find(e=>e.route===s.href),c="/playground"===s.href?{onMouseEnter:()=>{if("/playground"===s.href){y(s.href,50).onMouseEnter();let e=new URLSearchParams(window.location.search).get("config");e&&b(e)}}}:y(s.href,50);return(0,t.jsx)(l(),{href:s.href,onClick:e=>{e.preventDefault(),o(s.href)},className:"sidebar-nav-item group flex items-center transition-all duration-200 ease-out w-full text-left ".concat(a?"active":""," ").concat(w?"":"collapsed"),title:w?void 0:s.label,...c,children:(0,t.jsxs)("div",{className:"relative flex items-center w-full overflow-hidden",children:[(0,t.jsxs)("div",{className:"relative flex items-center justify-center transition-all duration-200 ease-out ".concat(w?"w-5 h-5 mr-3":"w-10 h-10 rounded-xl"," ").concat(!w&&a?"bg-white shadow-sm":w?"":"bg-transparent hover:bg-white/10"),children:[(0,t.jsx)(r,{className:"transition-all duration-200 ease-out ".concat("h-5 w-5"," ").concat(a?"text-orange-500":"text-white")}),n&&!a&&(0,t.jsx)("div",{className:"absolute rounded-full bg-blue-400 animate-pulse transition-all duration-200 ease-out ".concat(w?"-top-1 -right-1 w-2 h-2":"-top-1 -right-1 w-3 h-3"),title:"Predicted next destination"})]}),(0,t.jsxs)("div",{className:"flex-1 transition-all duration-200 ease-out ".concat(w?"opacity-100 translate-x-0 max-w-full":"opacity-0 translate-x-4 max-w-0"),children:[(0,t.jsxs)("div",{className:"flex items-center justify-between whitespace-nowrap",children:[(0,t.jsx)("div",{className:"font-medium text-sm",children:s.label}),i&&!a&&(0,t.jsx)("span",{className:"text-xs px-1.5 py-0.5 rounded-full ml-2 ".concat("high"===i.priority?"bg-blue-500/20 text-blue-300":"bg-gray-500/20 text-gray-300"),children:"high"===i.priority?"!":"\xb7"})]}),(0,t.jsx)("div",{className:"text-xs transition-colors duration-200 whitespace-nowrap ".concat(a?"text-orange-400":"text-gray-400"),children:i?i.reason:s.description})]})]})},s.href)})})]})})})}},95494:(e,s,a)=>{a.d(s,{A:()=>u});var t=a(95155),r=a(6874),l=a.n(r),n=a(12115),i=a(27016),c=a(22261),d=a(35695),o=a(83298),m=a(52643),x=a(90441);function u(){var e,s,r,u,g,h,p;let{isCollapsed:y,isHovered:f,toggleSidebar:b}=(0,c.c)(),j=(0,d.usePathname)(),{user:v,subscriptionStatus:N}=(0,o.R)(),[w,k]=(0,n.useState)(!1),[_,C]=(0,n.useState)(!1),[A,P]=(0,n.useState)(!1),S=(0,m.createSupabaseBrowserClient)();(0,n.useEffect)(()=>{let e=()=>{P(window.innerWidth>=1024)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]);let R=(null==v||null==(e=v.user_metadata)?void 0:e.first_name)||(null==v||null==(r=v.user_metadata)||null==(s=r.full_name)?void 0:s.split(" ")[0])||"User",E=R.charAt(0).toUpperCase()+((null==v||null==(h=v.user_metadata)||null==(g=h.last_name)||null==(u=g.charAt(0))?void 0:u.toUpperCase())||(null==(p=R.charAt(1))?void 0:p.toUpperCase())||"U"),L=(e=>{switch(e){case"/dashboard":return{title:"Dashboard",subtitle:"Overview & analytics"};case"/playground":return{title:"Playground",subtitle:"Test your models"};case"/my-models":return{title:"My Models",subtitle:"API key management"};case"/routing-setup":return{title:"Routing Setup",subtitle:"Configure routing"};case"/logs":return{title:"Logs",subtitle:"Request history"};case"/training":return{title:"Prompt Engineering",subtitle:"Custom prompts"};case"/analytics":return{title:"Analytics",subtitle:"Advanced insights"};case"/add-keys":return{title:"Add Keys",subtitle:"API key setup"};default:return{title:"Dashboard",subtitle:"Overview"}}})(j),I=(null==N?void 0:N.tier)==="free"?"Free Plan":(null==N?void 0:N.tier)==="starter"?"Starter Plan":(null==N?void 0:N.tier)==="professional"?"Professional Plan":(null==N?void 0:N.tier)==="enterprise"?"Enterprise Plan":"Free Plan",T=async()=>{try{let{clearAllUserCache:e}=await a.e(5738).then(a.bind(a,63171));await e(),await S.auth.signOut(),window.location.href="/auth/signin"}catch(e){try{localStorage.clear(),sessionStorage.clear()}catch(e){}window.location.href="/auth/signin"}};return(0,n.useEffect)(()=>{let e=e=>{(e.metaKey||e.ctrlKey)&&"k"===e.key&&(e.preventDefault(),C(!0))};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[]),(0,t.jsxs)("nav",{className:"header border-b border-gray-200 bg-white/95 backdrop-blur-sm w-full",children:[(0,t.jsx)("div",{className:"px-4 sm:px-6 lg:px-8 ".concat(A&&(!y||f)?"max-w-7xl mx-auto":A?"max-w-none":"max-w-7xl mx-auto"),children:(0,t.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("button",{onClick:b,className:"lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200",title:"Toggle sidebar",children:(0,t.jsx)(i.tK,{className:"h-6 w-6 text-gray-600"})}),(0,t.jsx)("div",{className:"lg:hidden",children:(0,t.jsx)("h1",{className:"text-xl font-bold text-gray-900",children:"RouKey"})}),(0,t.jsxs)("div",{className:"hidden lg:flex items-center space-x-2 text-sm text-gray-600",children:[(0,t.jsx)("span",{children:L.title}),(0,t.jsx)("span",{children:"/"}),(0,t.jsx)("span",{className:"text-gray-900 font-medium",children:L.subtitle})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 sm:space-x-4",children:[(0,t.jsxs)("div",{className:"hidden xl:block relative",children:[(0,t.jsxs)("button",{onClick:()=>C(!0),className:"w-64 pl-10 pr-4 py-2.5 text-sm bg-white border border-gray-200 rounded-lg text-gray-500 hover:text-gray-700 hover:border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-500/20 focus:border-orange-500 transition-all duration-200 text-left flex items-center justify-between",children:[(0,t.jsx)("span",{children:"Search..."}),(0,t.jsx)("kbd",{className:"hidden sm:inline-flex items-center px-2 py-1 text-xs font-medium text-gray-400 bg-gray-100 border border-gray-200 rounded",children:"⌘K"})]}),(0,t.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)(i.$p,{className:"h-4 w-4 text-gray-400"})})]}),(0,t.jsx)("button",{onClick:()=>C(!0),className:"xl:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200",children:(0,t.jsx)(i.$p,{className:"h-5 w-5 text-gray-600"})}),(0,t.jsxs)("button",{className:"p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 relative",children:[(0,t.jsx)(i.XF,{className:"h-5 w-5 text-gray-600"}),(0,t.jsx)("span",{className:"absolute -top-1 -right-1 h-3 w-3 bg-orange-500 rounded-full"})]}),(0,t.jsxs)("div",{className:"hidden sm:block relative",children:[(0,t.jsxs)("button",{onClick:()=>k(!w),className:"p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 flex items-center space-x-1",children:[(0,t.jsx)(i.Vy,{className:"h-5 w-5 text-gray-600"}),(0,t.jsx)(i.D3,{className:"h-3 w-3 text-gray-600 transition-transform duration-200 ".concat(w?"rotate-180":"")})]}),w&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"fixed inset-0 z-10",onClick:()=>k(!1)}),(0,t.jsxs)("div",{className:"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-20",children:[(0,t.jsxs)(l(),{href:"/settings",className:"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200",onClick:()=>k(!1),children:[(0,t.jsx)(i.Vy,{className:"h-4 w-4 mr-3 text-gray-500"}),"Account Settings"]}),(0,t.jsxs)(l(),{href:"/billing",className:"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200",onClick:()=>k(!1),children:[(0,t.jsx)(i.BF,{className:"h-4 w-4 mr-3 text-gray-500"}),"Billing & Plans"]}),(0,t.jsxs)(l(),{href:"/docs",className:"flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200",onClick:()=>k(!1),children:[(0,t.jsx)(i.AQ,{className:"h-4 w-4 mr-3 text-gray-500"}),"Documentation"]}),(0,t.jsx)("hr",{className:"my-1 border-gray-200"}),(0,t.jsxs)("button",{onClick:T,className:"flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors duration-200",children:[(0,t.jsx)(i.Rz,{className:"h-4 w-4 mr-3 text-red-500"}),"Sign Out"]})]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3 px-2 sm:px-3 py-2 rounded-lg hover:bg-gray-100 transition-colors duration-200 cursor-pointer",children:[(0,t.jsx)("div",{className:"w-8 h-8 rounded-full bg-gradient-to-br from-orange-400 to-orange-500 flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-white font-semibold text-sm",children:E})}),(0,t.jsxs)("div",{className:"hidden md:block",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-900",children:R}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:I})]})]})]})]})}),(0,t.jsx)(x.K,{isOpen:_,onClose:()=>C(!1)})]})}},96364:(e,s,a)=>{a.d(s,{V:()=>l});var t=a(95155);a(12115);var r=a(82880);let l=e=>{let{senderName:s,roleId:a}=e,l=(e=>{if(!e||"moderator"===e)return"from-blue-500 to-blue-600";let s=["from-green-500 to-green-600","from-purple-500 to-purple-600","from-orange-500 to-orange-600","from-pink-500 to-pink-600","from-indigo-500 to-indigo-600","from-teal-500 to-teal-600","from-red-500 to-red-600","from-yellow-500 to-yellow-600"];return s[e.split("").reduce((e,s)=>e+s.charCodeAt(0),0)%s.length]})(a),n=!a||"moderator"===a;return(0,t.jsx)("div",{className:"flex justify-start mb-4 opacity-75",children:(0,t.jsxs)("div",{className:"flex items-start space-x-3 max-w-[85%]",children:[(0,t.jsx)("div",{className:"flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r ".concat(l," flex items-center justify-center text-white shadow-sm animate-pulse"),children:(e=>e&&"moderator"!==e?(0,t.jsx)(r.Y,{className:"w-4 h-4"}):(0,t.jsx)(r.B,{className:"w-4 h-4"}))(a)}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,t.jsx)("span",{className:"text-sm font-semibold ".concat(n?"text-blue-700":"text-gray-700"),children:s}),(0,t.jsx)("span",{className:"text-xs text-gray-500",children:(e=>{let s=["is thinking...","is working on this...","is analyzing...","is processing...","is crafting a response..."];return s[e.split("").reduce((e,s)=>e+s.charCodeAt(0),0)%s.length]})(s)})]}),(0,t.jsx)("div",{className:"inline-block px-4 py-3 rounded-2xl shadow-sm ".concat(n?"bg-blue-50 border border-blue-100":"bg-gray-50 border border-gray-100"),children:(0,t.jsx)("div",{className:"flex items-center space-x-1",children:(0,t.jsxs)("div",{className:"flex space-x-1",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"0ms"}}),(0,t.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"150ms"}}),(0,t.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full animate-bounce",style:{animationDelay:"300ms"}})]})})})]})]})})}}}]);