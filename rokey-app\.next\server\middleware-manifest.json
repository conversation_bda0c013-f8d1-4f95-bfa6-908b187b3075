{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|public\\/).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|public/).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "R8Yp2nol-0wiYB1u7DO6q", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "QIO2FxewUlXf/7AHIT2B/6cD212Lh2bC+3YhJISHrok=", "__NEXT_PREVIEW_MODE_ID": "ee6abd9f7c1214a459f3f27ba8d1d6b6", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "235b17aa9ee6ab6fc1ac07b77b5c78b29b30ffe9bcd27a86ae88cbab190a0142", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "88544c0e79f7d882fee5f23b1cbcf74d5923f3cce8c2203d4423f71df999eb8b"}}}, "functions": {"/api/external/v1/chat/completions/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/chat/completions/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/app/api/external/v1/chat/completions/route.js"], "name": "app/api/external/v1/chat/completions/route", "page": "/api/external/v1/chat/completions/route", "matchers": [{"regexp": "^/api/external/v1/chat/completions$", "originalSource": "/api/external/v1/chat/completions"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "R8Yp2nol-0wiYB1u7DO6q", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "QIO2FxewUlXf/7AHIT2B/6cD212Lh2bC+3YhJISHrok=", "__NEXT_PREVIEW_MODE_ID": "ee6abd9f7c1214a459f3f27ba8d1d6b6", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "235b17aa9ee6ab6fc1ac07b77b5c78b29b30ffe9bcd27a86ae88cbab190a0142", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "88544c0e79f7d882fee5f23b1cbcf74d5923f3cce8c2203d4423f71df999eb8b"}}}, "sortedMiddleware": ["/"]}