(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5690],{17974:(e,a,s)=>{"use strict";s.d(a,{BZ:()=>r.A,Gg:()=>i.A,OR:()=>n.A,Zu:()=>t.A});var t=s(78039),r=s(90345),i=s(62486),n=s(67508)},38152:(e,a,s)=>{"use strict";s.d(a,{Pi:()=>t.A,fK:()=>i.A,uc:()=>r.A});var t=s(55628),r=s(31151),i=s(74500)},39499:(e,a,s)=>{"use strict";s.d(a,{Gg:()=>i.A,JD:()=>r.A,Kp:()=>t.A});var t=s(15713),r=s(15442),i=s(27305)},47321:(e,a,s)=>{"use strict";s.d(a,{C1:()=>t.A,Pi:()=>r.A,qh:()=>i.A});var t=s(6865),r=s(55628),i=s(52589)},68492:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>y});var t=s(95155),r=s(12115),i=s(6874),n=s.n(i),l=s(72227),o=s(94038),c=s(61316),d=s(85037),m=s(31151),x=s(80377),u=s(87162),h=s(74338),g=s(28003),f=s(60993),j=s(83298);function y(){let[e,a]=(0,r.useState)([]),[s,i]=(0,r.useState)(!0),[y,N]=(0,r.useState)(null),[p,b]=(0,r.useState)(""),[w,v]=(0,r.useState)(!1),A=(0,u.Z)(),[C,k]=(0,r.useState)(!1),{createHoverPrefetch:M,prefetchManageKeysData:P}=(0,g._)(),{subscriptionStatus:S,user:T}=(0,j.R)(),D=async()=>{i(!0),N(null);try{let e=await fetch("/api/custom-configs");if(!e.ok){let a=await e.json();throw Error(a.error||"Failed to fetch configurations")}let s=await e.json();a(s)}catch(e){N(e.message)}finally{i(!1)}};(0,r.useEffect)(()=>{T?D():null===T&&i(!1)},[T]);let E=async e=>{if(e.preventDefault(),!p.trim())return void N("Configuration name cannot be empty.");v(!0),N(null);try{let e=await fetch("/api/custom-configs",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:p})}),a=await e.json();if(!e.ok)throw Error(a.details||a.error||"Failed to create configuration");b(""),k(!1),await D()}catch(e){N(e.message)}finally{v(!1)}},I=(e,a)=>{A.showConfirmation({title:"Delete Configuration",message:'Are you sure you want to delete "'.concat(a,'"? This will permanently remove the configuration and all associated API keys. This action cannot be undone.'),confirmText:"Delete Configuration",cancelText:"Cancel",type:"danger"},async()=>{N(null);try{let a=await fetch("/api/custom-configs/".concat(e),{method:"DELETE"}),s=await a.json();if(!a.ok)throw Error(s.details||s.error||"Failed to delete configuration");await D()}catch(e){throw N("Failed to delete: ".concat(e.message)),e}})};return(0,t.jsxs)("div",{className:"space-y-8 animate-fade-in",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-4xl font-bold text-gray-900",children:"My API Models"}),(0,t.jsx)("p",{className:"text-gray-600 mt-2",children:"Manage your custom API configurations and keys"})]}),(0,t.jsx)(f.sU,{feature:"configurations",currentCount:e.length,customMessage:"You've reached your configuration limit. Upgrade to create more API configurations and organize your models better.",fallback:(0,t.jsxs)("div",{className:"flex flex-col items-end gap-2",children:[(0,t.jsxs)("button",{disabled:!0,className:"btn-primary opacity-50 cursor-not-allowed",children:[(0,t.jsx)(d.A,{className:"h-4 w-4 mr-2"}),"Create New Model"]}),(0,t.jsx)("p",{className:"text-xs text-orange-600 font-medium",children:(null==S?void 0:S.tier)==="free"?"Upgrade to Starter for more configurations":"Configuration limit reached - upgrade for more"})]}),children:(0,t.jsxs)("button",{onClick:()=>k(!C),className:C?"btn-secondary":"btn-primary",children:[(0,t.jsx)(d.A,{className:"h-4 w-4 mr-2"}),C?"Cancel":"Create New Model"]})})]}),S&&(0,t.jsx)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg px-4 py-2",children:(0,t.jsx)(f.Jg,{current:e.length,limit:"free"===S.tier?1:"starter"===S.tier?5:"professional"===S.tier?20:999999,label:"API Configurations",tier:S.tier,showUpgradeHint:!0})}),y&&(0,t.jsx)("div",{className:"card border-red-200 bg-red-50 p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-red-500 rounded-full"}),(0,t.jsx)("p",{className:"text-red-800",children:y})]})}),C&&(0,t.jsxs)("div",{className:"card max-w-md animate-scale-in p-6",children:[(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Create New Model"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Set up a new API configuration"})]}),(0,t.jsxs)("form",{onSubmit:E,className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"configName",className:"block text-sm font-medium text-gray-700 mb-2",children:"Model Name"}),(0,t.jsx)("input",{type:"text",id:"configName",value:p,onChange:e=>b(e.target.value),required:!0,className:"form-input",placeholder:"e.g., My Main Chat Assistant"})]}),(0,t.jsx)("button",{type:"submit",disabled:w,className:"btn-primary w-full",children:w?"Creating...":"Create Model"})]})]}),s&&(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:Array.from({length:6}).map((e,a)=>(0,t.jsx)(h.B0,{},a))}),!s&&!e.length&&!y&&!C&&(0,t.jsx)("div",{className:"card text-center py-12",children:(0,t.jsxs)("div",{className:"max-w-md mx-auto",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-orange-50 rounded-2xl flex items-center justify-center mx-auto mb-6 border border-orange-100",children:(0,t.jsx)(o.A,{className:"h-8 w-8 text-orange-600"})}),(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"No API Models Yet"}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:"Create your first API model configuration to get started with RoKey."}),(0,t.jsx)(f.sU,{feature:"configurations",currentCount:e.length,customMessage:"Create your first API configuration to get started with RouKey. Free tier includes 1 configuration.",fallback:(0,t.jsxs)("div",{className:"flex flex-col items-center gap-2",children:[(0,t.jsxs)("button",{disabled:!0,className:"btn-primary opacity-50 cursor-not-allowed",children:[(0,t.jsx)(d.A,{className:"h-4 w-4 mr-2"}),"Create Your First Model"]}),(0,t.jsx)("p",{className:"text-xs text-orange-600 font-medium",children:"Upgrade to create configurations"})]}),children:(0,t.jsxs)("button",{onClick:()=>k(!0),className:"btn-primary",children:[(0,t.jsx)(d.A,{className:"h-4 w-4 mr-2"}),"Create Your First Model"]})})]})}),!s&&e.length>0&&(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.map((e,a)=>(0,t.jsxs)("div",{className:"card p-6 hover:shadow-lg transition-all duration-200 animate-slide-in",style:{animationDelay:"".concat(100*a,"ms")},children:[(0,t.jsxs)("div",{className:"flex items-start justify-between mb-4",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2 truncate",children:e.name}),(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center text-xs text-gray-600",children:[(0,t.jsx)(o.A,{className:"h-3 w-3 mr-1"}),"ID: ",e.id.slice(0,8),"..."]}),(0,t.jsxs)("div",{className:"flex items-center text-xs text-gray-600",children:[(0,t.jsx)(l.A,{className:"h-3 w-3 mr-1"}),"Created: ",new Date(e.created_at).toLocaleDateString()]})]})]}),(0,t.jsx)("div",{className:"w-12 h-12 bg-orange-50 rounded-xl flex items-center justify-center shrink-0 border border-orange-100",children:(0,t.jsx)(o.A,{className:"h-6 w-6 text-orange-600"})})]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 mt-6",children:[(0,t.jsx)(n(),{href:"/my-models/".concat(e.id),className:"flex-1",...M(e.id),children:(0,t.jsxs)("button",{className:"btn-primary w-full",children:[(0,t.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Manage Keys"]})}),(0,t.jsxs)("button",{onClick:()=>I(e.id,e.name),className:"btn-secondary text-red-600 hover:text-red-700 hover:bg-red-50",children:[(0,t.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Delete"]})]})]},e.id))}),(0,t.jsx)(x.A,{isOpen:A.isOpen,onClose:A.hideConfirmation,onConfirm:A.onConfirm,title:A.title,message:A.message,confirmText:A.confirmText,cancelText:A.cancelText,type:A.type,isLoading:A.isLoading})]})}},69528:(e,a,s)=>{Promise.resolve().then(s.bind(s,68492))}},e=>{var a=a=>e(e.s=a);e.O(0,[8888,1459,5738,9968,6060,6308,4755,563,2662,8669,4703,622,2432,408,6642,7706,7544,2138,4518,9248,2324,7358],()=>a(69528)),_N_E=e.O()}]);