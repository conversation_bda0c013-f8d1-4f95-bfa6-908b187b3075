import { NextRequest, NextResponse } from 'next/server';
import { createServiceRoleClient } from '@/lib/supabase/service-role';
import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2025-02-24.acacia',
});

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const sessionId = searchParams.get('session_id');
    const plan = searchParams.get('plan');

    if (!sessionId) {
      console.error('No session ID provided');
      return NextResponse.redirect(new URL('/pricing?error=no_session', req.url));
    }

    console.log('Processing payment success for session:', sessionId);

    // Retrieve the checkout session from Stripe
    const session = await stripe.checkout.sessions.retrieve(sessionId);
    
    if (!session) {
      console.error('Session not found:', sessionId);
      return NextResponse.redirect(new URL('/pricing?error=session_not_found', req.url));
    }

    // Get user ID from session metadata
    const userId = session.metadata?.user_id;
    
    if (!userId || userId === 'pending_signup') {
      console.error('No user ID in session metadata');
      return NextResponse.redirect(new URL('/pricing?error=no_user_id', req.url));
    }

    console.log('Found user ID in session:', userId);

    const supabase = createServiceRoleClient();

    // Check if user is now active (webhook should have processed by now)
    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('user_status, subscription_status, subscription_tier')
      .eq('id', userId)
      .single();

    if (profileError) {
      console.error('Error fetching user profile:', profileError);
      return NextResponse.redirect(new URL('/pricing?error=profile_error', req.url));
    }

    if (!profile) {
      console.error('User profile not found for user:', userId);
      return NextResponse.redirect(new URL('/pricing?error=profile_not_found', req.url));
    }

    console.log('User profile status:', profile);

    // If user is not yet active, wait a moment for webhook processing
    if (profile.user_status !== 'active') {
      console.log('User not yet active, checking again in a moment...');
      
      // Wait 2 seconds and check again
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const { data: updatedProfile } = await supabase
        .from('user_profiles')
        .select('user_status, subscription_status, subscription_tier')
        .eq('id', userId)
        .single();

      if (updatedProfile?.user_status !== 'active') {
        console.log('User still not active after wait, but payment succeeded - activating manually');
        
        // Manually activate the user (webhook might be delayed)
        const { error: updateError } = await supabase
          .from('user_profiles')
          .update({
            user_status: 'active',
            subscription_status: 'active',
            updated_at: new Date().toISOString(),
          })
          .eq('id', userId);

        if (updateError) {
          console.error('Error manually activating user:', updateError);
        } else {
          console.log('User manually activated after payment success');
        }
      }
    }

    // Get user details for auto sign-in
    const { data: userData, error: userError } = await supabase.auth.admin.getUserById(userId);

    if (userError || !userData.user) {
      console.error('Error fetching user for session creation:', userError);
      return NextResponse.redirect(new URL(`/success?session_id=${sessionId}&plan=${plan}&manual_signin=true`, req.url));
    }

    console.log('User found, redirecting to success page with auto sign-in');

    // Redirect to success page with user details for auto sign-in
    const successUrl = new URL('/success', req.url);
    successUrl.searchParams.set('session_id', sessionId);
    successUrl.searchParams.set('plan', plan || '');
    successUrl.searchParams.set('user_id', userId);
    successUrl.searchParams.set('email', userData.user.email!);
    successUrl.searchParams.set('auto_signin', 'true');

    return NextResponse.redirect(successUrl);

  } catch (error) {
    console.error('Error in payment success handler:', error);
    return NextResponse.redirect(new URL('/pricing?error=payment_success_error', req.url));
  }
}
