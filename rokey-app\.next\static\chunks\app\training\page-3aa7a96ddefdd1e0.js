(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7637],{3408:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>d});var s=n(95155),r=n(12115),a=n(73360),i=n(60993),o=n(83298),l=n(87162),c=n(80377);function d(){let{subscriptionStatus:e}=(0,o.R)(),t=(0,l.Z)(),[n,d]=(0,r.useState)([]),[m,u]=(0,r.useState)(""),[h,p]=(0,r.useState)([]),[x,g]=(0,r.useState)(!1),[f,y]=(0,r.useState)(null),[b,v]=(0,r.useState)(null),[j,w]=(0,r.useState)(0),[k,A]=(0,r.useState)(""),N=async e=>{if(e)try{let n=await fetch("/api/training/jobs?custom_api_config_id=".concat(e));if(n.ok){let e=await n.json();if(e.length>0){var t;let n=e[0];(null==(t=n.training_data)?void 0:t.raw_prompts)&&A(n.training_data.raw_prompts)}}}catch(e){}};(0,r.useEffect)(()=>{(async()=>{try{let e=await fetch("/api/custom-configs");if(!e.ok)throw Error("Failed to fetch configurations");let t=await e.json();d(t),t.length>0&&(u(t[0].id),N(t[0].id))}catch(e){y("Failed to load configurations: ".concat(e.message))}})()},[]),(0,r.useEffect)(()=>{m&&N(m)},[m]);let _=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;try{let s=Date.now(),r=await fetch("/api/documents/list?configId=".concat(e,"&_t=").concat(s),{cache:"no-store"});if(r.ok){var n;let e=(null==(n=(await r.json()).documents)?void 0:n.length)||0;w(e)}else t<1&&setTimeout(()=>_(e,t+1),1e3)}catch(n){t<1&&setTimeout(()=>_(e,t+1),1e3)}};(0,r.useEffect)(()=>{m&&_(m)},[m]);let C=e=>{let t={system_instructions:"",examples:[],behavior_guidelines:"",general_instructions:""};for(let n of e.split("\n").filter(e=>e.trim())){let e=n.trim();if(e.startsWith("SYSTEM:"))t.system_instructions+=e.replace("SYSTEM:","").trim()+"\n";else if(e.startsWith("BEHAVIOR:"))t.behavior_guidelines+=e.replace("BEHAVIOR:","").trim()+"\n";else if(e.includes("→")||e.includes("->")){let n=e.includes("→")?"→":"->",s=e.split(n);if(s.length>=2){let e=s[0].trim(),r=s.slice(1).join(n).trim();t.examples.push({input:e,output:r})}}else e.length>0&&(t.general_instructions+=e+"\n")}return t},S=async()=>{if(!m||!k.trim())return void y("Please select an API configuration and provide training prompts.");if(!x){g(!0),y(null),v(null);try{var e;let t=C(k),s=(null==(e=n.find(e=>e.id===m))?void 0:e.name)||"Unknown Config",r={custom_api_config_id:m,name:"".concat(s," Training - ").concat(new Date().toLocaleDateString()),description:"Training job for ".concat(s," with ").concat(t.examples.length," examples"),training_data:{processed_prompts:t,raw_prompts:k.trim(),last_prompt_update:new Date().toISOString()},parameters:{training_type:"prompt_engineering",created_via:"training_page",version:"1.0"}},a=await fetch("/api/training/jobs/upsert",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)});if(!a.ok){let e=await a.text();throw Error("Failed to save training job: ".concat(a.status," ").concat(e))}let i=await a.json(),o="updated"===i.operation,l="".concat(o?"\uD83D\uDD04":"\uD83C\uDF89"," Prompt Engineering ").concat(o?"updated":"completed"," successfully!\n\n")+'Your "'.concat(s,'" configuration has been ').concat(o?"updated":"enhanced"," with:\n")+"• ".concat(t.examples.length," training examples\n")+"• Custom system instructions and behavior guidelines\n\n✨ All future chats using this configuration will automatically:\n• Follow your training examples\n• Apply your behavior guidelines\n• Maintain consistent personality and responses\n\n"+"\uD83D\uDE80 Try it now in the Playground to see your ".concat(o?"updated":"enhanced"," model in action!\n\n")+"\uD83D\uDCA1 Your training prompts remain here so you can modify them anytime.";v(l)}catch(e){y("Failed to create prompt engineering: ".concat(e.message))}finally{g(!1)}}};return(0,s.jsx)("div",{className:"min-h-screen bg-[#faf8f5] p-6",children:(0,s.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsx)("h1",{className:"text-4xl font-bold text-gray-900",children:"AI Training & Enhancement"}),e&&(0,s.jsx)(i.yA,{tier:e.tier,size:"lg"})]}),(0,s.jsx)("p",{className:"text-lg text-gray-600 max-w-3xl",children:"Enhance your AI models with custom prompts and knowledge documents. Upload your proprietary content to create domain-specific AI assistants."})]}),f&&(0,s.jsx)("div",{className:"mb-6 bg-red-50 border border-red-200 rounded-xl p-4",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("svg",{className:"w-5 h-5 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,s.jsx)("p",{className:"text-red-800 text-sm font-medium",children:f})]})}),b&&(0,s.jsx)("div",{className:"mb-6 bg-green-50 border border-green-200 rounded-xl p-4",children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("svg",{className:"w-5 h-5 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),(0,s.jsx)("p",{className:"text-green-800 text-sm font-medium",children:b})]})}),(0,s.jsx)(i.sU,{feature:"knowledge_base",customMessage:"Knowledge base document upload is available starting with the Professional plan. Upload documents to enhance your AI with proprietary knowledge and create domain-specific assistants.",children:(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-lg p-8 mb-8",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3 mb-6",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center",children:(0,s.jsx)("svg",{className:"w-5 h-5 text-orange-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Knowledge Documents"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Upload documents to enhance your AI with proprietary knowledge"})]})]}),e&&m&&(0,s.jsx)("div",{className:"mb-4 bg-gray-50 border border-gray-200 rounded-lg px-4 py-2",children:(0,s.jsx)(i.Jg,{current:j,limit:"professional"===e.tier?5:999999*("enterprise"===e.tier),label:"Knowledge Base Documents",tier:e.tier,showUpgradeHint:!0})}),(0,s.jsx)(a.A,{configId:m,onDocumentUploaded:()=>{m&&setTimeout(()=>{_(m)},500)},onDocumentDeleted:()=>{m&&setTimeout(()=>{_(m)},500)}})]})}),(0,s.jsx)(i.sU,{feature:"prompt_engineering",customMessage:"Prompt engineering is available starting with the Starter plan. Create custom prompts to define AI behavior, provide examples, and enhance your model's responses.",children:(0,s.jsxs)("div",{className:"bg-white rounded-2xl shadow-lg p-8",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3 mb-6",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center",children:(0,s.jsx)("svg",{className:"w-5 h-5 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Custom Prompts"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"Define behavior, examples, and instructions for your AI"})]})]}),(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"configSelect",className:"block text-sm font-medium text-gray-700 mb-2",children:"Select API Configuration"}),(0,s.jsxs)("select",{id:"configSelect",value:m,onChange:e=>u(e.target.value),className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm",children:[(0,s.jsx)("option",{value:"",children:"Choose which model to train..."}),n.map(e=>(0,s.jsx)("option",{value:e.id,children:e.name},e.id))]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"trainingPrompts",className:"block text-sm font-medium text-gray-700 mb-2",children:"Custom Prompts & Instructions"}),(0,s.jsx)("textarea",{id:"trainingPrompts",value:k,onChange:e=>A(e.target.value),placeholder:"Enter your training prompts using these formats:\n\nSYSTEM: You are a helpful customer service agent for our company\nBEHAVIOR: Always be polite and offer solutions\n\nUser asks about returns → I'd be happy to help with your return! Let me check our policy for you.\nCustomer is frustrated → I understand your frustration. Let me see how I can resolve this for you.\n\nGeneral instructions can be written as regular text.",rows:12,className:"w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors bg-white text-sm resize-none font-mono"}),(0,s.jsxs)("div",{className:"mt-3 bg-blue-50 border border-blue-200 rounded-lg p-3",children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-blue-900 mb-2",children:"Training Format Guide:"}),(0,s.jsxs)("ul",{className:"text-xs text-blue-800 space-y-1",children:[(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"SYSTEM:"})," Core instructions for the AI's role and personality"]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"BEHAVIOR:"})," Guidelines for how the AI should behave"]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"Examples:"}),' Use "User input → Expected response" format']}),(0,s.jsxs)("li",{children:[(0,s.jsx)("strong",{children:"General:"})," Any other instructions written as normal text"]})]})]})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center pt-6 border-t border-gray-200",children:[(0,s.jsx)("div",{className:"flex space-x-3",children:(0,s.jsx)("button",{type:"button",className:"btn-secondary",onClick:()=>{t.showConfirmation({title:"Clear Training Prompts",message:"Are you sure you want to clear all training prompts? This will remove all your custom instructions, examples, and behavior guidelines. This action cannot be undone.",confirmText:"Clear All",cancelText:"Cancel",type:"warning"},()=>{A("")})},children:"Clear Form"})}),(0,s.jsx)("button",{type:"button",onClick:S,disabled:!m||!k.trim()||x,className:"btn-primary disabled:opacity-50 disabled:cursor-not-allowed",children:x?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Processing Prompts..."]}):"Save Prompts"})]})]})]})}),(0,s.jsx)(c.A,{isOpen:t.isOpen,onClose:t.hideConfirmation,onConfirm:t.onConfirm,title:t.title,message:t.message,confirmText:t.confirmText,cancelText:t.cancelText,type:t.type,isLoading:t.isLoading})]})})}},17974:(e,t,n)=>{"use strict";n.d(t,{BZ:()=>r.A,Gg:()=>a.A,OR:()=>i.A,Zu:()=>s.A});var s=n(78039),r=n(90345),a=n(62486),i=n(67508)},38152:(e,t,n)=>{"use strict";n.d(t,{Pi:()=>s.A,fK:()=>a.A,uc:()=>r.A});var s=n(55628),r=n(31151),a=n(74500)},39499:(e,t,n)=>{"use strict";n.d(t,{Gg:()=>a.A,JD:()=>r.A,Kp:()=>s.A});var s=n(15713),r=n(15442),a=n(27305)},42005:(e,t,n)=>{Promise.resolve().then(n.bind(n,3408))},47321:(e,t,n)=>{"use strict";n.d(t,{C1:()=>s.A,Pi:()=>r.A,qh:()=>a.A});var s=n(6865),r=n(55628),a=n(52589)},91480:(e,t,n)=>{"use strict";n.d(t,{RI:()=>r,rA:()=>a,ZH:()=>i,iU:()=>o,kr:()=>l,_O:()=>c,X:()=>d.A});var s=n(19946);let r=(0,s.A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]),a=(0,s.A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),i=(0,s.A)("file",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}]]),o=(0,s.A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]),l=(0,s.A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]),c=(0,s.A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]);var d=n(54416)}},e=>{var t=t=>e(e.s=t);e.O(0,[8888,1459,5738,9968,6060,6308,4755,563,2662,8669,4703,622,2432,408,6642,7706,7544,2138,4518,9248,2324,7358],()=>t(42005)),_N_E=e.O()}]);