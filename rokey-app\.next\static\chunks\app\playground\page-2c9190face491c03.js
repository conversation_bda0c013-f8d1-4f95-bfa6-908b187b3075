(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3882],{11485:(e,t,a)=>{"use strict";a.d(t,{B:()=>n.A,v:()=>r.A});var r=a(30192),n=a(86474)},22261:(e,t,a)=>{"use strict";a.d(t,{G:()=>i,c:()=>l});var r=a(95155),n=a(12115);let s=(0,n.createContext)(void 0);function i(e){let{children:t}=e,[a,i]=(0,n.useState)(!0),[l,o]=(0,n.useState)(!1),[c,d]=(0,n.useState)(!1);return(0,r.jsx)(s.Provider,{value:{isCollapsed:a,isHovered:l,isHoverDisabled:c,toggleSidebar:()=>i(!a),collapseSidebar:()=>i(!0),expandSidebar:()=>i(!1),setHovered:e=>{c||o(e)},setHoverDisabled:e=>{d(e),e&&o(!1)}},children:t})}function l(){let e=(0,n.useContext)(s);if(void 0===e)throw Error("useSidebar must be used within a SidebarProvider");return e}},24766:(e,t,a)=>{Promise.resolve().then(a.bind(a,79021))},34908:(e,t,a)=>{"use strict";a.d(t,{S:()=>r.A,X:()=>n.A});var r=a(29337),n=a(45754)},57514:(e,t,a)=>{"use strict";a.d(t,{D:()=>n.A,E:()=>r.A});var r=a(5279),n=a(63418)},59513:(e,t,a)=>{"use strict";a.d(t,{EF:()=>r.A,DQ:()=>n.A,C1:()=>s.A,Pp:()=>l,DP:()=>o.A,nr:()=>c,Y3:()=>d,XL:()=>u.A,$p:()=>h.A,R2:()=>m.A,P:()=>x.A,Zu:()=>g.A,BZ:()=>p.A,Gg:()=>f.A,K6:()=>y.A});var r=a(5279),n=a(64274),s=a(6865),i=a(12115);let l=i.forwardRef(function(e,t){let{title:a,titleId:r,...n}=e;return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},n),a?i.createElement("title",{id:r},a):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 0 0 2.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 0 0-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 0 0 .75-.75 2.25 2.25 0 0 0-.1-.664m-5.8 0A2.251 2.251 0 0 1 13.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25ZM6.75 12h.008v.008H6.75V12Zm0 3h.008v.008H6.75V15Zm0 3h.008v.008H6.75V18Z"}))});var o=a(5246);let c=i.forwardRef(function(e,t){let{title:a,titleId:r,...n}=e;return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},n),a?i.createElement("title",{id:r},a):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m6.75 7.5 3 2.25-3 2.25m4.5 0h3m-9 8.25h13.5A2.25 2.25 0 0 0 21 18V6a2.25 2.25 0 0 0-2.25-2.25H5.25A2.25 2.25 0 0 0 3 6v12a2.25 2.25 0 0 0 2.25 2.25Z"}))}),d=i.forwardRef(function(e,t){let{title:a,titleId:r,...n}=e;return i.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},n),a?i.createElement("title",{id:r},a):null,i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.362 5.214A8.252 8.252 0 0 1 12 21 8.25 8.25 0 0 1 6.038 7.047 8.287 8.287 0 0 0 9 9.601a8.983 8.983 0 0 1 3.361-6.867 8.21 8.21 0 0 0 3 2.48Z"}),i.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 18a3.75 3.75 0 0 0 .495-7.468 5.99 5.99 0 0 0-1.925 3.547 5.975 5.975 0 0 1-2.133-1.001A3.75 3.75 0 0 0 12 18Z"}))});var u=a(48666),h=a(78046),m=a(61316),x=a(65946),g=a(8246),p=a(86474),f=a(27305),y=a(64219)},64134:(e,t,a)=>{"use strict";a.d(t,{g:()=>n});var r=a(12115);let n=r.forwardRef(function(e,t){let{title:a,titleId:n,...s}=e;return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":n},s),a?r.createElement("title",{id:n},a):null,r.createElement("path",{fillRule:"evenodd",d:"M5.25 2.25a3 3 0 0 0-3 3v4.318a3 3 0 0 0 .879 2.121l9.58 9.581c.92.92 2.39 1.186 3.548.428a18.849 18.849 0 0 0 5.441-5.44c.758-1.16.492-2.629-.428-3.548l-9.58-9.581a3 3 0 0 0-2.122-.879H5.25ZM6.375 7.5a1.125 1.125 0 1 0 0-2.25 1.125 1.125 0 0 0 0 2.25Z",clipRule:"evenodd"}))})},79021:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>N});var r=a(95155),n=a(12115);let s=n.forwardRef(function(e,t){let{title:a,titleId:r,...s}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),a?n.createElement("title",{id:r},a):null,n.createElement("path",{fillRule:"evenodd",d:"M19.916 4.626a.75.75 0 0 1 .208 1.04l-9 13.5a.75.75 0 0 1-1.154.114l-6-6a.75.75 0 0 1 1.06-1.06l5.353 5.353 8.493-12.74a.75.75 0 0 1 1.04-.207Z",clipRule:"evenodd"}))}),i=n.forwardRef(function(e,t){let{title:a,titleId:r,...s}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),a?n.createElement("title",{id:r},a):null,n.createElement("path",{d:"M3.478 2.404a.75.75 0 0 0-.926.941l2.432 7.905H13.5a.75.75 0 0 1 0 1.5H4.984l-2.432 7.905a.75.75 0 0 0 .926.94 60.519 60.519 0 0 0 18.445-8.986.75.75 0 0 0 0-1.218A60.517 60.517 0 0 0 3.478 2.404Z"}))}),l=n.forwardRef(function(e,t){let{title:a,titleId:r,...s}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),a?n.createElement("title",{id:r},a):null,n.createElement("path",{fillRule:"evenodd",d:"M18.97 3.659a2.25 2.25 0 0 0-3.182 0l-10.94 10.94a3.75 3.75 0 1 0 5.304 5.303l7.693-7.693a.75.75 0 0 1 1.06 1.06l-7.693 7.693a5.25 5.25 0 1 1-7.424-7.424l10.939-10.94a3.75 3.75 0 1 1 5.303 5.304L9.097 18.835l-.008.008-.007.007-.002.002-.003.002A2.25 2.25 0 0 1 5.91 15.66l7.81-7.81a.75.75 0 0 1 1.061 1.06l-7.81 7.81a.75.75 0 0 0 1.054 1.068L18.97 6.84a2.25 2.25 0 0 0 0-3.182Z",clipRule:"evenodd"}))}),o=n.forwardRef(function(e,t){let{title:a,titleId:r,...s}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),a?n.createElement("title",{id:r},a):null,n.createElement("path",{fillRule:"evenodd",d:"M16.5 4.478v.227a48.816 48.816 0 0 1 3.878.512.75.75 0 1 1-.256 1.478l-.209-.035-1.005 13.07a3 3 0 0 1-2.991 2.77H8.084a3 3 0 0 1-2.991-2.77L4.087 6.66l-.209.035a.75.75 0 0 1-.256-1.478A48.567 48.567 0 0 1 7.5 4.705v-.227c0-1.564 1.213-2.9 2.816-2.951a52.662 52.662 0 0 1 3.369 0c1.603.051 2.815 1.387 2.815 2.951Zm-6.136-1.452a51.196 51.196 0 0 1 3.273 0C14.39 3.05 15 3.684 15 4.478v.113a49.488 49.488 0 0 0-6 0v-.113c0-.794.609-1.428 1.364-1.452Zm-.355 5.945a.75.75 0 1 0-1.5.058l.347 9a.75.75 0 1 0 1.499-.058l-.346-9Zm5.48.058a.75.75 0 1 0-1.498-.058l-.347 9a.75.75 0 0 0 1.5.058l.345-9Z",clipRule:"evenodd"}))}),c=n.forwardRef(function(e,t){let{title:a,titleId:r,...s}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),a?n.createElement("title",{id:r},a):null,n.createElement("path",{fillRule:"evenodd",d:"M12 2.25c-5.385 0-9.75 4.365-9.75 9.75s4.365 9.75 9.75 9.75 9.75-4.365 9.75-9.75S17.385 2.25 12 2.25Zm-1.72 6.97a.75.75 0 1 0-1.06 1.06L10.94 12l-1.72 1.72a.75.75 0 1 0 1.06 1.06L12 13.06l1.72 1.72a.75.75 0 1 0 1.06-1.06L13.06 12l1.72-1.72a.75.75 0 1 0-1.06-1.06L12 10.94l-1.72-1.72Z",clipRule:"evenodd"}))}),d=n.forwardRef(function(e,t){let{title:a,titleId:r,...s}=e;return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:t,"aria-labelledby":r},s),a?n.createElement("title",{id:r},a):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10"}))});var u=a(79112),h=a(95803),m=a(8413),x=a(43456),g=a(4654),p=a(22261),f=a(24403),y=a(41e3),v=a(83298);a(96121);let w=(0,n.lazy)(()=>Promise.all([a.e(3466),a.e(5738),a.e(9968),a.e(6060),a.e(6308),a.e(4755),a.e(563),a.e(2662),a.e(8669),a.e(4703),a.e(622),a.e(2432),a.e(408),a.e(5721)]).then(a.bind(a,35079)).then(e=>({default:e.OrchestrationCanvas}))),b=(e,t)=>{},j=n.memo(e=>{let{chat:t,currentConversation:a,onLoadChat:n,onDeleteChat:s}=e,i=(null==a?void 0:a.id)===t.id;return(0,r.jsxs)("div",{className:"relative group p-3 hover:bg-gray-50 rounded-xl transition-all duration-200 ".concat(i?"bg-orange-50 border border-orange-200":""),children:[(0,r.jsx)("button",{onClick:()=>n(t),className:"w-full text-left",children:(0,r.jsx)("div",{className:"flex items-start justify-between",children:(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-900 truncate mb-1",children:t.title}),t.last_message_preview&&(0,r.jsx)("p",{className:"text-xs text-gray-500 line-clamp-2 mb-2",children:t.last_message_preview}),(0,r.jsxs)("div",{className:"flex items-center justify-between text-xs text-gray-400",children:[(0,r.jsxs)("span",{children:[t.message_count," messages"]}),(0,r.jsx)("span",{children:new Date(t.updated_at).toLocaleDateString()})]})]})})}),(0,r.jsx)("button",{onClick:e=>{e.stopPropagation(),s(t.id)},className:"absolute top-2 right-2 opacity-0 group-hover:opacity-100 p-1 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded transition-all duration-200",title:"Delete conversation",children:(0,r.jsx)(o,{className:"w-4 h-4"})})]})});function N(){var e,t,a;let{isCollapsed:o,isHovered:N,setHoverDisabled:S}=(0,p.c)(),{user:k}=(0,v.R)(),C=!o||N?"256px":"64px",T=(null==k||null==(e=k.user_metadata)?void 0:e.first_name)||(null==k||null==(a=k.user_metadata)||null==(t=a.full_name)?void 0:t.split(" ")[0])||"",[E,A]=(0,n.useState)([]),[_,O]=(0,n.useState)(""),[L,M]=(0,n.useState)(!0),H=(0,n.useCallback)(async e=>{if(e)try{let t=await fetch("/api/keys?custom_config_id=".concat(e),{cache:"force-cache",headers:{"Cache-Control":"max-age=300"}});t.ok&&await t.json()}catch(e){}},[]);(0,n.useEffect)(()=>{if(_){let e=setTimeout(()=>{H(_)},1e3);return()=>clearTimeout(e)}},[_,H]);let[I,D]=(0,n.useState)(""),[R,P]=(0,n.useState)([]),[z,B]=(0,n.useState)(!1),[W,F]=(0,n.useState)(null),[Y,Z]=(0,n.useState)(!0),[U,V]=(0,n.useState)(!1),[J,q]=(0,n.useState)([]),[K,X]=(0,n.useState)([]),Q=(0,n.useRef)(null),G=(0,n.useRef)(null),$=(0,n.useRef)(null),[ee,et]=(0,n.useState)(!1),[ea,er]=(0,n.useState)(null),[en,es]=(0,n.useState)(null),[ei,el]=(0,n.useState)(""),[eo,ec]=(0,n.useState)(!1),[ed,eu]=(0,n.useState)(null),[eh,em]=(0,n.useState)(!1),[ex,eg]=(0,n.useState)(!1),[ep,ef]=(0,n.useState)(!1),[ey,ev]=(0,n.useState)(!1),[ew,eb]=(0,n.useState)(!1);(0,n.useEffect)(()=>{S(ep&&!ey)},[ep,ey,S]),(0,n.useEffect)(()=>{},[eh,ed,ep,ey]);let ej=(0,y.w6)({enableAutoProgression:!0,onStageChange:void 0}),[eN,eS]=(0,n.useState)(""),ek=(e,t)=>{let a="";if(e.includes("\uD83C\uDFAC **Multi-Role AI Orchestration Started!**"))a="Multi-Role AI Orchestration Started";else if(e.includes("\uD83D\uDCCB **Orchestration Plan:**"))a="Planning specialist assignments";else if(e.includes("\uD83E\uDD16 **Moderator:**"))a="Moderator coordinating specialists";else if(e.includes("Specialist:")&&e.includes("Working...")){let t=e.match(/(\w+)\s+Specialist:/);a=t?"".concat(t[1]," Specialist working"):"Specialist working on your request"}else e.includes("\uD83C\uDFAD **SYNTHESIS PHASE INITIATED**")?a="Synthesizing specialist responses":e.includes("Analyzing and processing")&&(a="Analyzing and processing with specialized expertise");a&&a!==eN&&(eS(a),t.updateOrchestrationStatus(a))},eC=async()=>{if(_&&ea){B(!0),eS("Continuing synthesis automatically..."),ej.startProcessing();try{let a={id:Date.now().toString()+"-continue",role:"user",content:[{type:"text",text:"continue"}]};P(e=>[...e,a]),await eU(ea.id,a);let r={custom_api_config_id:_,messages:[...R.map(e=>({role:e.role,content:1===e.content.length&&"text"===e.content[0].type?e.content[0].text:e.content})),{role:"user",content:"continue"}],stream:Y,...(null==k?void 0:k.id)&&{_internal_user_id:k.id}},n=await fetch("/api/v1/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat("Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13")},body:JSON.stringify(r),cache:"no-store"});if(n.ok){let a,r=await n.text();try{a=JSON.parse(r)}catch(e){a=null}if((null==a?void 0:a.error)==="synthesis_complete"){P(e=>e.slice(0,-1)),B(!1),eS(""),ej.markComplete(),D("continue"),setTimeout(()=>{e1()},100);return}let s=new Response(r,{status:n.status,statusText:n.statusText,headers:n.headers});if(Y&&s.body){let a=s.body.getReader(),r=new TextDecoder,n=Date.now().toString()+"-assistant-continue",i={id:n,role:"assistant",content:[{type:"text",text:""}]};P(e=>[...e,i]);let l="",o=!1,c=null,d=s.headers.get("X-Synthesis-Progress"),u=s.headers.get("X-Synthesis-Complete"),h=null!==d;for(h?(ej.markStreaming(),eS("")):(ej.markOrchestrationStarted(),eS("Continuing synthesis..."),c=setTimeout(()=>{o||(ej.markStreaming(),eS(""))},800));;){let{done:s,value:d}=await a.read();if(s)break;for(let a of r.decode(d,{stream:!0}).split("\n"))if(a.startsWith("data: ")){let r=a.substring(6);if("[DONE]"===r.trim())break;try{var e,t;let a=JSON.parse(r);if(a.choices&&(null==(t=a.choices[0])||null==(e=t.delta)?void 0:e.content)){let e=a.choices[0].delta.content;l+=e,!h&&!o&&(e.includes("\uD83C\uDFAC **Multi-Role AI Orchestration Started!**")||e.includes("\uD83D\uDCCB **Orchestration Plan:**")||e.includes("\uD83C\uDFAD **SYNTHESIS PHASE INITIATED**")||e.includes("\uD83E\uDD16 **Moderator:**")||e.includes("Specialist:"))?(o=!0,c&&(clearTimeout(c),c=null),ek(e,ej)):!h&&o&&ek(e,ej);let t=i.content[0];t.text=l,P(e=>e.map(e=>e.id===n?{...e,content:[t]}:e))}}catch(e){}}}if(c&&clearTimeout(c),l){let e={...i,content:[{type:"text",text:l}]};h&&"true"!==u&&l.includes("[SYNTHESIS CONTINUES AUTOMATICALLY...]")?(await eU(ea.id,e),setTimeout(()=>{eC()},1e3)):await eU(ea.id,e)}}}else throw Error("Auto-continuation failed: ".concat(n.status))}catch(t){let e={id:Date.now().toString()+"-error-continue",role:"error",content:[{type:"text",text:"Auto-continuation failed: ".concat(t instanceof Error?t.message:"Unknown error")}]};P(t=>[...t,e])}finally{B(!1),eS(""),ej.markComplete()}}},{chatHistory:eT,isLoading:eE,isStale:eA,error:e_,refetch:eO,prefetch:eL,invalidateCache:eM}=(0,f.mx)({configId:_,enablePrefetch:!0,cacheTimeout:3e5,staleTimeout:3e4}),{prefetchChatHistory:eH}=(0,f.l2)(),eI=(0,n.useMemo)(()=>[{id:"write-copy",title:"Write copy",description:"Create compelling marketing content",icon:"✍️",color:"bg-amber-100 text-amber-700",prompt:"Help me write compelling copy for my product landing page"},{id:"image-generation",title:"Image generation",description:"Create visual content descriptions",icon:"\uD83C\uDFA8",color:"bg-blue-100 text-blue-700",prompt:"Help me create detailed prompts for AI image generation"},{id:"create-avatar",title:"Create avatar",description:"Design character personas",icon:"\uD83D\uDC64",color:"bg-green-100 text-green-700",prompt:"Help me create a detailed character avatar for my story"},{id:"write-code",title:"Write code",description:"Generate and debug code",icon:"\uD83D\uDCBB",color:"bg-purple-100 text-purple-700",prompt:"Help me write clean, efficient code for my project"}],[]);(0,n.useEffect)(()=>{let e=async()=>{let e=performance.now();try{L&&await new Promise(e=>setTimeout(e,50));let t=await fetch("/api/custom-configs");if(!t.ok){let e=await t.json();throw Error(e.error||"Failed to fetch configurations")}let a=await t.json();A(a),a.length>0&&O(a[0].id),M(!1),b("Config fetch",e)}catch(t){F("Failed to load configurations: ".concat(t.message)),A([]),M(!1),b("Config fetch (failed)",e)}};k&&L&&e()},[L,k]);let eD=e=>new Promise((t,a)=>{let r=new FileReader;r.readAsDataURL(e),r.onload=()=>t(r.result),r.onerror=e=>a(e)}),eR=async e=>{let t=Array.from(e.target.files||[]);if(0===t.length)return;let a=J.length,r=t.slice(0,10-a);r.length<t.length&&F("You can only upload up to 10 images. ".concat(t.length-r.length," images were not added."));try{let e=[];for(let t of r){let a=await eD(t);e.push(a)}q(e=>[...e,...r]),X(t=>[...t,...e])}catch(e){F("Failed to process one or more images. Please try again.")}Q.current&&(Q.current.value="")},eP=e=>{void 0!==e?(q(t=>t.filter((t,a)=>a!==e)),X(t=>t.filter((t,a)=>a!==e))):(q([]),X([])),Q.current&&(Q.current.value="")},ez=function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];$.current&&$.current.scrollTo({top:$.current.scrollHeight,behavior:e?"smooth":"auto"})},eB=(0,n.useMemo)(()=>R.length>100?R.slice(-50):R,[R]);(0,n.useEffect)(()=>{R.length>0&&requestAnimationFrame(()=>{ez()})},[R.length]),(0,n.useEffect)(()=>{z&&R.length>0&&requestAnimationFrame(()=>{ez()})},[R,z]),(0,n.useEffect)(()=>{if(z&&R.length>0){let e=R[R.length-1];e&&"assistant"===e.role&&requestAnimationFrame(()=>{ez()})}},[R,z]),(0,n.useEffect)(()=>{let e=setTimeout(()=>{R.length>0&&requestAnimationFrame(()=>{if($.current){let e=$.current;e.scrollHeight-e.scrollTop-e.clientHeight<100&&ez()}})},200);return()=>clearTimeout(e)},[o,N,ee,R.length]),(0,n.useEffect)(()=>{if(_&&E.length>0){let e=E.filter(e=>e.id!==_).slice(0,3),t=setTimeout(()=>{e.forEach(e=>{eH(e.id)})},2e3);return()=>clearTimeout(t)}},[_,E,eH]);let eW=async function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];t||ec(!0);try{let a=t?R.length:0,r=Date.now(),n=await fetch("/api/chat/messages?conversation_id=".concat(e.id,"&limit=").concat(25,"&offset=").concat(a,"&latest=").concat(!t,"&_cb=").concat(r),{cache:"no-store",headers:{"Cache-Control":"no-cache"}});if(!n.ok)throw Error("Failed to load conversation messages");let s=(await n.json()).map(e=>({id:e.id,role:e.role,content:e.content.map(e=>{var t;return"text"===e.type&&e.text?{type:"text",text:e.text}:"image_url"===e.type&&(null==(t=e.image_url)?void 0:t.url)?{type:"image_url",image_url:{url:e.image_url.url}}:{type:"text",text:""}})}));t?P(e=>[...s,...e]):(P(s),ea&&ea.id===e.id||er(e)),F(null)}catch(e){F("Failed to load conversation: ".concat(e.message))}finally{t||ec(!1)}},eF=async()=>{if(!_||0===R.length)return null;try{let e=null==ea?void 0:ea.id;if(!e){let t=R[0],a="New Chat";if(t&&t.content.length>0){let e=t.content.find(e=>"text"===e.type);e&&e.text&&(a=e.text.slice(0,50)+(e.text.length>50?"...":""))}let r={custom_api_config_id:_,title:a},n=await fetch("/api/chat/conversations",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)});if(!n.ok)throw Error("Failed to create conversation");let s=await n.json();e=s.id,er(s)}for(let t of R){if(t.id.includes("-")&&t.id.length>20)continue;let a={conversation_id:e,role:t.role,content:t.content};await fetch("/api/chat/messages",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)})}return ea||eO(!0),e}catch(e){return F("Failed to save conversation: ".concat(e.message)),null}},eY=async e=>{try{if(!(await fetch("/api/chat/conversations?id=".concat(e),{method:"DELETE"})).ok)throw Error("Failed to delete conversation");(null==ea?void 0:ea.id)===e&&(er(null),P([])),eO(!0)}catch(e){F("Failed to delete conversation: ".concat(e.message))}},eZ=async e=>{if(!_)return null;try{let t="New Chat";if(e.content.length>0){let a=e.content.find(e=>"text"===e.type);a&&a.text&&(t=a.text.slice(0,50)+(a.text.length>50?"...":""))}let a={custom_api_config_id:_,title:t},r=await fetch("/api/chat/conversations",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!r.ok)throw Error("Failed to create conversation");let n=await r.json();return er(n),n.id}catch(e){return F("Failed to create conversation: ".concat(e.message)),null}},eU=async(e,t)=>{try{let a={conversation_id:e,role:t.role,content:t.content},r=await fetch("/api/chat/messages",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!r.ok)throw Error("Failed to save message");return await r.json()}catch(e){}},eV=e=>{D(e),setTimeout(()=>{let e=document.querySelector('textarea[placeholder*="Type a message"]');e&&(e.focus(),e.setSelectionRange(e.value.length,e.value.length))},100)},eJ=async()=>{R.length>0&&await eF(),P([]),er(null),D(""),F(null),eP(),ej.reset()},eq=(0,n.useCallback)(async e=>{if(e===_)return;R.length>0&&await eJ(),O(e);let t=E.find(t=>t.id===e);t&&t.name},[_,R.length,eJ,O,E]),eK=async e=>{er(e),P([]),D(""),F(null),eP();let t=(async()=>{if(R.length>0&&!ea)try{await eF()}catch(e){}})();try{await eW(e)}catch(e){F("Failed to load conversation: ".concat(e.message))}await t},eX=(e,t)=>{es(e),el(t)},eQ=()=>{es(null),el("")},eG=async()=>{if(!en||!ei.trim()||!_)return;let e=R.findIndex(e=>e.id===en);if(-1===e)return;let t=[...R];t[e]={...t[e],content:[{type:"text",text:ei.trim()}]};let a=t.slice(0,e+1);if(P(a),es(null),el(""),ea)try{if(R.slice(e+1).length>0){let t=R[e];if(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(t.id)){let e=await fetch("/api/chat/messages?conversation_id=".concat(ea.id,"&limit=1&latest=false"));if(e.ok){let a=(await e.json()).find(e=>e.id===t.id);if(a){let e=new Date(a.created_at).getTime(),t=await fetch("/api/chat/messages/delete-after-timestamp",{method:"DELETE",headers:{"Content-Type":"application/json"},body:JSON.stringify({conversation_id:ea.id,after_timestamp:e})});t.ok&&await t.json()}}}else{let e=parseInt(t.id)||Date.now(),a=await fetch("/api/chat/messages/delete-after-timestamp",{method:"DELETE",headers:{"Content-Type":"application/json"},body:JSON.stringify({conversation_id:ea.id,after_timestamp:e})});a.ok&&await a.json()}}let t=a[e];if(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(t.id)){let e=await fetch("/api/chat/messages?id=".concat(t.id),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({content:t.content})});if(e.ok)await e.json();else{let t=await e.text();throw Error("Failed to update message: ".concat(t))}}else await eU(ea.id,t);eO(!0),Object.keys(localStorage).filter(e=>e.startsWith("chat_")||e.startsWith("conversation_")).forEach(e=>localStorage.removeItem(e))}catch(e){F("Failed to update conversation: ".concat(e.message))}await e$(a)},e$=async e=>{if(!_||0===e.length)return;B(!0),F(null),ej.startProcessing();let t={custom_api_config_id:_,messages:e.filter(e=>"user"===e.role||"assistant"===e.role||"system"===e.role).map(e=>{let t;if("system"===e.role){let a=e.content[0];t=a&&"text"===a.type?a.text:""}else t=1===e.content.length&&"text"===e.content[0].type?e.content[0].text:e.content.map(e=>"image_url"===e.type?{type:"image_url",image_url:{url:e.image_url.url}}:{type:"text",text:e.text});return{role:e.role,content:t}}),stream:Y,...(null==k?void 0:k.id)&&{_internal_user_id:k.id}};try{var a,r,n,s,i,l,o;ej.updateStage("connecting"),performance.now();let e=await fetch("/api/v1/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat("Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13")},body:JSON.stringify(t),cache:"no-store"});if(performance.now(),!e.ok){let t=await e.json();throw Error(t.error||"API Error: ".concat(e.statusText," (Status: ").concat(e.status,")"))}if(ej.analyzeResponseHeaders(e.headers),setTimeout(()=>{Y&&ej.markStreaming()},400),Y&&e.body){let t=e.body.getReader(),n=new TextDecoder,s=Date.now().toString()+"-assistant",i={id:s,role:"assistant",content:[{type:"text",text:""}]};P(e=>[...e,i]);let l="",o=!1,c=null,d="";for(c=setTimeout(()=>{o||ej.markStreaming()},400);;){let{done:e,value:u}=await t.read();if(e)break;for(let e of n.decode(u,{stream:!0}).split("\n"))if(e.startsWith("data: ")){let t=e.substring(6);if("[DONE]"===t.trim())break;try{let e=JSON.parse(t);if("orchestration.progress"===e.object){o=!0,d=e.data.message,c&&(clearTimeout(c),c=null),ej.markOrchestrationStarted(),eS(d);return}if(e.choices&&(null==(r=e.choices[0])||null==(a=r.delta)?void 0:a.content)){let t=e.choices[0].delta.content;l+=t,!o&&(t.includes("\uD83C\uDFAC **Multi-Role AI Orchestration Started!**")||t.includes("\uD83D\uDCCB **Orchestration Plan:**")||t.includes("\uD83C\uDFAD **SYNTHESIS PHASE INITIATED**")||t.includes("\uD83E\uDD16 **Moderator:**")||t.includes("Specialist:"))&&(o=!0,c&&(clearTimeout(c),c=null),ej.markOrchestrationStarted()),o&&!d&&ek(t,ej);let a=i.content[0];a.text=l,P(e=>e.map(e=>e.id===s?{...e,content:[a]}:e))}}catch(e){}}}if(c&&clearTimeout(c),l&&ea){let e={...i,content:[{type:"text",text:l}]};l.includes("[SYNTHESIS CONTINUES AUTOMATICALLY...]")||l.includes("*The response will continue automatically in a new message...*")?(await eU(ea.id,e),setTimeout(()=>{eC()},2e3)):await eU(ea.id,e)}}else{let t=await e.json(),a="Could not parse assistant's response.";(null==(i=t.choices)||null==(s=i[0])||null==(n=s.message)?void 0:n.content)?a=t.choices[0].message.content:(null==(o=t.content)||null==(l=o[0])?void 0:l.text)?a=t.content[0].text:"string"==typeof t.text&&(a=t.text);let r={id:Date.now().toString()+"-assistant",role:"assistant",content:[{type:"text",text:a}]};P(e=>[...e,r]),ea&&await eU(ea.id,r)}}catch(t){let e={id:Date.now().toString()+"-error",role:"error",content:[{type:"text",text:t.message||"An unexpected error occurred."}]};P(t=>[...t,e]),F(t.message)}finally{B(!1),ej.markComplete()}},e0=async(e,t)=>{if(!_||e<0||e>=R.length||"assistant"!==R[e].role)return;B(!0),F(null),eS(""),ej.startProcessing();let a=R.slice(0,e);if(P(a),ea)try{if(R.slice(e).length>0){let t=R[e],a=parseInt(t.id)||Date.now(),r=await fetch("/api/chat/messages/delete-after-timestamp",{method:"DELETE",headers:{"Content-Type":"application/json"},body:JSON.stringify({conversation_id:ea.id,from_timestamp:a})});r.ok&&await r.json()}eO(!0)}catch(e){}let r={custom_api_config_id:_,messages:a.filter(e=>"user"===e.role||"assistant"===e.role||"system"===e.role).map(e=>{let t;if("system"===e.role){let a=e.content[0];t=a&&"text"===a.type?a.text:""}else t=1===e.content.length&&"text"===e.content[0].type?e.content[0].text:e.content.map(e=>"image_url"===e.type?{type:"image_url",image_url:{url:e.image_url.url}}:{type:"text",text:e.text});return{role:e.role,content:t}}),stream:Y,...t&&{specific_api_key_id:t},...(null==k?void 0:k.id)&&{_internal_user_id:k.id}};try{ej.updateStage("connecting"),performance.now();let e=await fetch("/api/v1/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat("Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13")},body:JSON.stringify(r),cache:"no-store"});if(performance.now(),!e.ok){let t=await e.json();throw Error(t.error||"HTTP ".concat(e.status,": ").concat(e.statusText))}ej.analyzeResponseHeaders(e.headers);let t=e.headers.get("X-RoKey-Orchestration-ID"),a=e.headers.get("X-RoKey-Orchestration-Active");if(t&&"true"===a&&(eu(t),em(!0),eg(!1)),setTimeout(()=>{Y&&ej.markStreaming()},400),Y&&e.body){let t=e.body.getReader(),a=new TextDecoder,r="",l={id:Date.now().toString()+"-assistant-retry",role:"assistant",content:[{type:"text",text:""}]};P(e=>[...e,l]);try{for(;;){let{done:e,value:o}=await t.read();if(e)break;for(let e of a.decode(o,{stream:!0}).split("\n"))if(e.startsWith("data: ")){let t=e.slice(6);if("[DONE]"===t)continue;try{var n,s,i;let e=JSON.parse(t);if(null==(i=e.choices)||null==(s=i[0])||null==(n=s.delta)?void 0:n.content){let t=e.choices[0].delta.content;r+=t,t.includes("\uD83C\uDFAC **Multi-Role AI Orchestration Started!**")||t.includes("\uD83D\uDCCB **Orchestration Plan:**")||t.includes("\uD83C\uDFAD **SYNTHESIS PHASE INITIATED**")||t.includes("\uD83E\uDD16 **Moderator:**")||t.includes("Specialist:")?(ej.markOrchestrationStarted(),ek(t,ej)):eN&&ek(t,ej),P(e=>e.map(e=>e.id===l.id?{...e,content:[{type:"text",text:r}]}:e))}}catch(e){}}}}finally{t.releaseLock()}if(r&&ea){let e={...l,content:[{type:"text",text:r}]};await eU(ea.id,e)}}else{let t=await e.json(),a="";t.choices&&t.choices.length>0&&t.choices[0].message?a=t.choices[0].message.content:t.content&&Array.isArray(t.content)&&t.content.length>0&&(a=t.content[0].text);let r={id:Date.now().toString()+"-assistant-retry",role:"assistant",content:[{type:"text",text:a}]};P(e=>[...e,r]),ea&&await eU(ea.id,r)}}catch(t){let e={id:Date.now().toString()+"-error-retry",role:"error",content:[{type:"text",text:t.message||"An unexpected error occurred during retry."}]};P(t=>[...t,e]),F(t.message),ea&&await eU(ea.id,e)}finally{B(!1),ej.markComplete()}},e1=(0,n.useCallback)(async e=>{if(e&&e.preventDefault(),!I.trim()&&0===J.length||!_)return;if("continue"===I.trim().toLowerCase()&&R.length>0){D(""),await eC();return}B(!0),F(null),eS(""),ej.startProcessing(),performance.now();let t=I.trim(),a=[...J],r=[...K];D(""),eP();let n=[],s=[];if(t&&(n.push({type:"text",text:t}),s.push({type:"text",text:t})),a.length>0)try{for(let e=0;e<a.length;e++){let t=a[e],i=r[e],l=await eD(t);n.push({type:"image_url",image_url:{url:i}}),s.push({type:"image_url",image_url:{url:l}})}}catch(e){F("Failed to process one or more images. Please try again."),B(!1),D(t),q(a),X(r);return}let i={id:Date.now().toString(),role:"user",content:n};P(e=>[...e,i]);let l=null==ea?void 0:ea.id,o=Promise.resolve(l||null);Promise.resolve(),l||ea||(o=eZ(i)),o.then(async e=>{e&&await eU(e,i)}).catch(e=>{});let c={custom_api_config_id:_,messages:[...R.filter(e=>"user"===e.role||"assistant"===e.role||"system"===e.role).map(e=>{let t;if("system"===e.role){let a=e.content[0];t=a&&"text"===a.type?a.text:""}else t=1===e.content.length&&"text"===e.content[0].type?e.content[0].text:e.content.map(e=>"image_url"===e.type?{type:"image_url",image_url:{url:e.image_url.url}}:{type:"text",text:e.text});return{role:e.role,content:t}}),{role:"user",content:1===s.length&&"text"===s[0].type?s[0].text:s}],stream:Y,...(null==k?void 0:k.id)&&{_internal_user_id:k.id}};try{ej.updateStage("connecting"),performance.now();let e=await fetch("/api/v1/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat("Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13")},body:JSON.stringify(c),cache:"no-store"});if(performance.now(),!e.ok){let t=await e.json();throw Error(t.error||"API Error: ".concat(e.statusText," (Status: ").concat(e.status,")"))}ej.analyzeResponseHeaders(e.headers);let t=e.headers.get("X-RoKey-Orchestration-ID"),a=e.headers.get("X-RoKey-Orchestration-Active");if(t&&"true"===a&&(eu(t),em(!0),eg(!1)),Y&&e.body){let t=e.body.getReader(),a=new TextDecoder,r=Date.now().toString()+"-assistant",n={id:r,role:"assistant",content:[{type:"text",text:""}]};P(e=>[...e,n]);let s="",i=!1,l=null,c="";for(l=setTimeout(()=>{i||ej.markStreaming()},400);;){let{done:e,value:o}=await t.read();if(e)break;for(let e of a.decode(o,{stream:!0}).split("\n"))if(e.startsWith("data: ")){let t=e.substring(6);if("[DONE]"===t.trim())break;try{var d,u;let e=JSON.parse(t);if("orchestration.progress"===e.object){i=!0,c=e.data.message,l&&(clearTimeout(l),l=null),ej.markOrchestrationStarted(),eS(c);continue}if(e.choices&&(null==(u=e.choices[0])||null==(d=u.delta)?void 0:d.content)){let t=e.choices[0].delta.content;s+=t,!i&&(t.includes("\uD83C\uDFAC **Multi-Role AI Orchestration Started!**")||t.includes("\uD83D\uDCCB **Orchestration Plan:**")||t.includes("\uD83C\uDFAD **SYNTHESIS PHASE INITIATED**")||t.includes("\uD83E\uDD16 **Moderator:**")||t.includes("Specialist:"))&&(i=!0,l&&(clearTimeout(l),l=null),ej.markOrchestrationStarted()),i&&!c&&ek(t,ej);let a=n.content[0];a.text=s,P(e=>e.map(e=>e.id===r?{...e,content:[a]}:e))}}catch(e){}}}if(l&&clearTimeout(l),s){let t={...n,content:[{type:"text",text:s}]},a=e.headers.get("X-Synthesis-Progress");e.headers.get("X-Synthesis-Complete"),s.includes("[SYNTHESIS CONTINUES AUTOMATICALLY...]")||s.includes("*The response will continue automatically in a new message...*")?(o.then(async e=>{e&&await eU(e,t)}),setTimeout(()=>{eC()},null!==a?1e3:2e3)):o.then(async e=>{e&&await eU(e,t)})}}}catch(t){let e={id:Date.now().toString()+"-error",role:"error",content:[{type:"text",text:t.message||"An unexpected error occurred."}]};P(t=>[...t,e]),F(t.message),o.then(async t=>{t&&await eU(t,e)}).catch(e=>{})}finally{B(!1),ej.markComplete(),(0,y.n4)(ej.stageHistory),performance.now(),o.then(async e=>{e&&!ea&&eO(!0)}).catch(e=>{})}},[I,J,_,z,ea,R,ej,Y,eN,P,F,B,er,D,q,X,eO]);return(0,r.jsxs)("div",{className:"min-h-screen bg-[#faf8f5] flex",children:[(0,r.jsxs)("div",{className:"flex-1 flex flex-col transition-all duration-300 ease-in-out",style:{marginLeft:C,marginRight:ep&&!ey?"50%":ee?"0px":"320px"},children:[(0,r.jsx)("div",{className:"fixed top-0 z-40 bg-[#faf8f5]/95 backdrop-blur-sm border-b border-gray-200/30 transition-all duration-300 ease-in-out",style:{left:C,right:ep&&!ey?"50%":ee?"0px":"320px"},children:(0,r.jsx)("div",{className:"px-6 py-2",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"flex items-center space-x-2",children:_?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-600",children:"Connected"})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-gray-400 rounded-full"}),(0,r.jsx)("span",{className:"text-sm font-medium text-gray-500",children:"Not Connected"})]})}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)("select",{value:_,onChange:e=>eq(e.target.value),disabled:0===E.length,className:"appearance-none px-4 py-2.5 pr-10 bg-white/90 border border-gray-200/50 rounded-xl text-sm font-medium text-gray-700 focus:outline-none focus:ring-2 focus:ring-orange-500/20 focus:border-orange-300 transition-all duration-200 shadow-sm hover:shadow-md min-w-[200px]",children:[(0,r.jsx)("option",{value:"",children:"Select Router"}),E.map(e=>(0,r.jsx)("option",{value:e.id,children:e.name},e.id))]}),(0,r.jsx)("div",{className:"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("span",{className:"text-sm font-medium text-gray-600",children:"Streaming"}),(0,r.jsx)("button",{onClick:()=>Z(!Y),className:"relative inline-flex h-7 w-12 items-center rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-orange-500/20 shadow-sm ".concat(Y?"bg-orange-500 shadow-orange-200":"bg-gray-300"),children:(0,r.jsx)("span",{className:"inline-block h-5 w-5 transform rounded-full bg-white transition-transform duration-200 shadow-sm ".concat(Y?"translate-x-6":"translate-x-1")})})]})]})})}),(0,r.jsx)("div",{className:"flex-1 flex flex-col pt-0 pb-32",children:0!==R.length||ea?(0,r.jsxs)("div",{className:"flex-1 relative ".concat(ep&&!ey?"overflow-visible":"overflow-hidden"),children:[(0,r.jsx)("div",{className:"h-full flex ".concat(ep&&!ey?"justify-start":"justify-center"),children:(0,r.jsx)("div",{ref:$,className:"w-full h-full overflow-y-auto px-6 transition-all duration-300 ".concat(ep&&!ey?"max-w-2xl -ml-32":"max-w-4xl"),onScroll:e=>{let t=e.currentTarget;V(!(t.scrollHeight-t.scrollTop-t.clientHeight<100)&&R.length>0)},children:(0,r.jsxs)("div",{className:"space-y-6 py-0",children:[ea&&R.length>=50&&(0,r.jsx)("div",{className:"text-center py-4",children:(0,r.jsx)("button",{onClick:()=>eW(ea,!0),disabled:eE,className:"px-4 py-2 text-sm text-orange-600 hover:text-orange-700 hover:bg-orange-50 rounded-lg transition-colors duration-200 disabled:opacity-50",children:eE?"Loading...":"Load Earlier Messages"})}),eo&&0===R.length&&(0,r.jsx)("div",{className:"space-y-4",children:Array.from({length:3}).map((e,t)=>(0,r.jsxs)("div",{className:"flex justify-start",children:[(0,r.jsx)("div",{className:"w-7 h-7 rounded-full bg-gray-200 animate-pulse mr-3 mt-1 flex-shrink-0"}),(0,r.jsx)("div",{className:"max-w-[65%] bg-gray-100 rounded-2xl rounded-bl-lg px-4 py-3 animate-pulse",children:(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-1/2"}),(0,r.jsx)("div",{className:"h-4 bg-gray-200 rounded w-5/6"})]})})]},t))}),R.length>100&&(0,r.jsx)("div",{className:"text-center py-4",children:(0,r.jsxs)("div",{className:"text-sm text-gray-500 bg-gray-50 rounded-lg px-4 py-2 inline-block",children:["Showing last 50 of ",R.length," messages for better performance"]})}),eB.map((e,t)=>(0,r.jsxs)("div",{className:"flex ".concat("user"===e.role?"justify-end":"justify-start"," group ").concat(ep&&!ey?"-ml-96":""," ").concat("assistant"===e.role&&ep&&!ey?"ml-8":""," ").concat(0===t?"pt-3":""),children:["assistant"===e.role&&(0,r.jsx)("div",{className:"w-7 h-7 rounded-full bg-orange-50 flex items-center justify-center mr-3 mt-1 flex-shrink-0 border border-orange-100",children:(0,r.jsx)("svg",{className:"w-3.5 h-3.5 text-orange-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})})}),(0,r.jsxs)("div",{className:"".concat("user"===e.role?ep&&!ey?"max-w-[60%]":"max-w-[50%]":ep&&!ey?"max-w-[85%]":"max-w-[75%]"," relative ").concat("user"===e.role?"bg-orange-600 text-white rounded-2xl rounded-br-lg shadow-sm":"assistant"===e.role?"text-gray-900":"system"===e.role?"bg-amber-50 text-amber-800 rounded-xl border border-amber-200":"bg-red-50 text-red-800 rounded-xl border border-red-200"," px-4 py-3 transition-all duration-300"),children:["user"===e.role&&(0,r.jsxs)("div",{className:"absolute -top-8 right-0 z-10 flex items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200",children:[(0,r.jsx)(h.A,{text:e.content.filter(e=>"text"===e.type).map(e=>e.text).join("\n"),variant:"message",size:"sm",title:"Copy message",className:"text-gray-500 hover:text-gray-700 hover:bg-white/20 rounded-lg cursor-pointer"}),(0,r.jsx)("button",{onClick:()=>eX(e.id,e.content.filter(e=>"text"===e.type).map(e=>e.text).join("\n")),className:"p-1.5 text-gray-500 hover:text-gray-700 hover:bg-white/20 rounded-lg transition-all duration-200 cursor-pointer",title:"Edit message",children:(0,r.jsx)(d,{className:"w-4 h-4 stroke-2"})})]}),"user"!==e.role&&(0,r.jsxs)("div",{className:"absolute -bottom-8 left-0 z-10 flex items-center space-x-2",children:[(0,r.jsx)(h.A,{text:e.content.filter(e=>"text"===e.type).map(e=>e.text).join("\n"),variant:"message",size:"sm",title:"Copy message"}),"assistant"===e.role&&_&&(0,r.jsx)(m.A,{configId:_,onRetry:e=>e0(t,e),disabled:z})]}),(0,r.jsx)("div",{className:"space-y-2 chat-message-content",children:"user"===e.role&&en===e.id?(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("textarea",{value:ei,onChange:e=>el(e.target.value),className:"w-full p-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50 resize-none",placeholder:"Edit your message...",rows:3,autoFocus:!0}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)("button",{onClick:eG,disabled:!ei.trim(),className:"flex items-center space-x-1 px-3 py-1.5 bg-white/20 hover:bg-white/30 disabled:bg-white/10 disabled:opacity-50 text-white text-sm rounded-lg transition-all duration-200",children:[(0,r.jsx)(s,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Save & Continue"})]}),(0,r.jsxs)("button",{onClick:eQ,className:"flex items-center space-x-1 px-3 py-1.5 bg-white/10 hover:bg-white/20 text-white text-sm rounded-lg transition-all duration-200",children:[(0,r.jsx)(c,{className:"w-4 h-4"}),(0,r.jsx)("span",{children:"Cancel"})]})]}),(0,r.jsx)("p",{className:"text-white/70 text-xs",children:"\uD83D\uDCA1 Saving will restart the conversation from this point, removing all messages that came after."})]}):e.content.map((t,a)=>{if("text"===t.type)if("assistant"===e.role)return(0,r.jsx)(u.A,{content:t.text,className:"text-sm"},a);else return(0,r.jsx)("div",{className:"whitespace-pre-wrap break-words leading-relaxed text-sm",children:t.text},a);return"image_url"===t.type?(0,r.jsx)("img",{src:t.image_url.url,alt:"uploaded content",className:"max-w-full max-h-48 rounded-xl shadow-sm"},a):null})})]}),"user"===e.role&&(0,r.jsx)("div",{className:"w-7 h-7 rounded-full bg-orange-600 flex items-center justify-center ml-3 mt-1 flex-shrink-0",children:(0,r.jsx)("svg",{className:"w-3.5 h-3.5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})})})]},e.id)),eh&&ed&&ey&&(0,r.jsx)("div",{className:"space-y-6",children:(0,r.jsx)(g.f,{orchestrationComplete:ex,onMaximize:()=>{eb(!0),setTimeout(()=>eb(!1),100)},isCanvasOpen:ep,isCanvasMinimized:ey})}),z&&(0,r.jsxs)("div",{className:"flex justify-start group",children:[(!eN||"typing"===ej.currentStage)&&(0,r.jsx)("div",{className:"w-7 h-7 rounded-full bg-orange-50 flex items-center justify-center mr-3 mt-1 flex-shrink-0 border border-orange-100",children:(0,r.jsx)("svg",{className:"w-3.5 h-3.5 text-orange-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})})}),(0,r.jsx)(x.A,{currentStage:ej.currentStage,isStreaming:Y&&"typing"===ej.currentStage,orchestrationStatus:eN,onStageChange:e=>{}})]}),eh&&ed&&(0,r.jsx)(n.Suspense,{fallback:(0,r.jsxs)("div",{className:"flex items-center justify-center p-8",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"}),(0,r.jsx)("span",{className:"ml-3 text-gray-600",children:"Loading orchestration canvas..."})]}),children:(0,r.jsx)(w,{executionId:ed,onCanvasStateChange:(e,t)=>{ef(e),ev(t),e&&!t&&et(!0)},forceMaximize:ew,onComplete:e=>{if(null==ed?void 0:ed.startsWith("test-execution-id"))return void eg(!0);eg(!0);let t={id:Date.now().toString()+"-orchestration-final",role:"assistant",content:[{type:"text",text:e}]};P(e=>[...e,t]),em(!1),eu(null),eg(!1),(null==ea?void 0:ea.id)&&eU(ea.id,t).catch(e=>{})},onError:e=>{null!=ed&&ed.startsWith("test-execution-id")||(F("Orchestration error: ".concat(e)),em(!1),eu(null))}})}),(0,r.jsx)("div",{ref:G})]})})}),U&&(0,r.jsx)("div",{className:"absolute bottom-6 left-1/2 transform -translate-x-1/2 z-10",children:(0,r.jsx)("button",{onClick:()=>ez(!0),className:"w-12 h-12 bg-white rounded-full shadow-lg border border-gray-200/50 flex items-center justify-center hover:shadow-xl transition-all duration-200 hover:scale-105 group","aria-label":"Scroll to bottom",children:(0,r.jsx)("svg",{className:"w-5 h-5 text-gray-600 group-hover:text-orange-600 transition-colors",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 14l-7 7m0 0l-7-7m7 7V3"})})})})]}):(0,r.jsx)("div",{className:"flex-1 flex items-center justify-center px-6 overflow-hidden",children:(0,r.jsx)("div",{className:"w-full mx-auto transition-all duration-300 ".concat(ep&&!ey?"max-w-2xl -ml-32":"max-w-4xl"),children:(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center",children:[(0,r.jsxs)("div",{className:"text-center mb-12",children:[(0,r.jsxs)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:["Welcome",T?" ".concat(T):""," to RouKey"]}),(0,r.jsx)("p",{className:"text-lg text-gray-600 max-w-md mx-auto",children:"Get started by selecting a router and choosing a conversation starter below. Not sure where to start?"})]}),(0,r.jsx)("div",{className:"grid grid-cols-2 gap-4 w-full max-w-2xl",children:eI.map(e=>(0,r.jsxs)("button",{onClick:()=>eV(e.prompt),disabled:!_,className:"group relative p-6 bg-white rounded-2xl border border-gray-200/50 hover:border-orange-300 hover:shadow-lg transition-all duration-200 text-left disabled:opacity-50 disabled:cursor-not-allowed ".concat(_?"cursor-pointer hover:scale-[1.02]":"cursor-not-allowed"),children:[(0,r.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,r.jsx)("div",{className:"w-12 h-12 rounded-xl flex items-center justify-center text-xl ".concat(e.color," group-hover:scale-110 transition-transform duration-200"),children:e.icon}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("h3",{className:"font-semibold text-gray-900 mb-1 group-hover:text-orange-600 transition-colors",children:e.title}),(0,r.jsx)("p",{className:"text-sm text-gray-600 leading-relaxed",children:e.description})]})]}),(0,r.jsx)("div",{className:"absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-200",children:(0,r.jsx)("svg",{className:"w-5 h-5 text-orange-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4l8 8-8 8M4 12h16"})})})]},e.id))})]})})})}),(0,r.jsx)("div",{className:"fixed bottom-0 z-50 transition-all duration-300 ease-in-out",style:{left:C,right:ep&&!ey?"50%":ee?"0px":"320px"},children:(0,r.jsx)("div",{className:"px-6 pt-3 pb-2 flex justify-center",children:(0,r.jsxs)("div",{className:"w-full transition-all duration-300 ".concat(ep&&!ey?"max-w-2xl":"max-w-4xl"),children:[W&&(0,r.jsx)("div",{className:"mb-4 bg-red-50 border border-red-200 rounded-2xl p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("svg",{className:"w-5 h-5 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,r.jsx)("p",{className:"text-red-800 text-sm font-medium",children:W})]})}),!1,(0,r.jsxs)("form",{onSubmit:e1,children:[K.length>0&&(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-orange-500 rounded-full"}),(0,r.jsxs)("span",{className:"text-sm font-medium text-gray-700",children:[K.length," image",K.length>1?"s":""," attached"]})]}),(0,r.jsx)("button",{type:"button",onClick:()=>eP(),className:"text-xs text-gray-500 hover:text-red-600 transition-colors duration-200 font-medium",children:"Clear all"})]}),(0,r.jsx)("div",{className:"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-3",children:K.map((e,t)=>(0,r.jsxs)("div",{className:"relative group",children:[(0,r.jsxs)("div",{className:"relative overflow-hidden rounded-xl border-2 border-gray-100 bg-white shadow-sm hover:shadow-md transition-all duration-200 aspect-square",children:[(0,r.jsx)("img",{src:e,alt:"Preview ".concat(t+1),className:"w-full h-full object-cover"}),(0,r.jsx)("div",{className:"absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-all duration-200"}),(0,r.jsx)("button",{type:"button",onClick:()=>eP(t),className:"absolute -top-1 -right-1 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center transition-all duration-200 shadow-lg opacity-0 group-hover:opacity-100 transform scale-90 group-hover:scale-100","aria-label":"Remove image ".concat(t+1),children:(0,r.jsx)(c,{className:"w-3.5 h-3.5"})})]}),(0,r.jsx)("div",{className:"absolute bottom-1 right-1 bg-black/70 text-white text-xs px-1.5 py-0.5 rounded-md font-medium",children:t+1})]},t))})]}),(0,r.jsx)("div",{className:"relative bg-white rounded-2xl border border-gray-200/50 shadow-lg hover:shadow-xl transition-all duration-200 focus-within:ring-2 focus-within:ring-orange-500/20 focus-within:border-orange-300",children:(0,r.jsxs)("div",{className:"flex items-end p-4 space-x-3",children:[(0,r.jsx)("input",{type:"file",accept:"image/*",multiple:!0,onChange:eR,ref:Q,className:"hidden",id:"imageUpload"}),(0,r.jsxs)("button",{type:"button",onClick:()=>{var e;return null==(e=Q.current)?void 0:e.click()},disabled:J.length>=10,className:"relative p-2 rounded-xl transition-all duration-200 flex-shrink-0 ".concat(J.length>=10?"text-gray-300 cursor-not-allowed":"text-gray-400 hover:text-orange-500 hover:bg-orange-50"),"aria-label":J.length>=10?"Maximum 10 images reached":"Attach images",title:J.length>=10?"Maximum 10 images reached":"Attach images (up to 10)",children:[(0,r.jsx)(l,{className:"w-5 h-5"}),J.length>0&&(0,r.jsx)("div",{className:"absolute -top-1 -right-1 w-4 h-4 bg-orange-500 text-white text-xs rounded-full flex items-center justify-center font-bold",children:J.length})]}),(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)("textarea",{value:I,onChange:e=>D(e.target.value),placeholder:_?"Type a message...":"Select a router first",disabled:!_||z,rows:1,className:"w-full px-0 py-2 bg-transparent border-0 text-gray-900 placeholder-gray-400 focus:outline-none disabled:opacity-50 resize-none text-base leading-relaxed",onKeyDown:e=>{"Enter"===e.key&&!e.shiftKey&&(e.preventDefault(),(I.trim()||J.length>0)&&_&&!z&&e1())},style:{minHeight:"24px",maxHeight:"120px"},onInput:e=>{let t=e.target;t.style.height="auto",t.style.height=Math.min(t.scrollHeight,120)+"px"}})}),(0,r.jsx)("button",{type:"submit",disabled:!_||z||!I.trim()&&0===J.length,className:"p-2.5 bg-orange-500 hover:bg-orange-600 disabled:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-xl transition-all duration-200 flex items-center justify-center shadow-lg hover:shadow-xl flex-shrink-0","aria-label":"Send message",title:"Send message",children:z?(0,r.jsx)("svg",{className:"w-5 h-5 animate-spin",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}):(0,r.jsx)(i,{className:"w-5 h-5"})})]})})]})]})})})]}),(0,r.jsx)("div",{className:"fixed top-0 right-0 h-full bg-white shadow-xl transition-all duration-300 ease-in-out z-30 ".concat(ee?"w-0 overflow-hidden":"w-80"),style:{transform:ee?"translateX(100%)":"translateX(0)",opacity:+!ee},children:(0,r.jsxs)("div",{className:"flex flex-col h-full",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200/50",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-4 h-4 text-orange-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"font-semibold text-gray-900",children:"History"}),(0,r.jsxs)("p",{className:"text-xs text-gray-500",children:[eT.length," conversations"]})]})]}),(0,r.jsx)("button",{onClick:()=>et(!ee),className:"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-all duration-200 hover:scale-105","aria-label":"Toggle history sidebar",children:(0,r.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,r.jsx)("div",{className:"p-4 border-b border-gray-200/50",children:(0,r.jsxs)("button",{onClick:eJ,className:"w-full flex items-center justify-center space-x-2 px-4 py-3 bg-orange-500 hover:bg-orange-600 text-white rounded-xl transition-all duration-200 shadow-sm hover:shadow-md",children:[(0,r.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),(0,r.jsx)("span",{className:"font-medium",children:"New Chat"})]})}),(0,r.jsx)("div",{className:"flex-1 overflow-y-auto p-4 space-y-2",children:eE?(0,r.jsx)("div",{className:"space-y-2 p-4",children:Array.from({length:8}).map((e,t)=>(0,r.jsxs)("div",{className:"p-3 rounded-xl border border-gray-100 animate-pulse",children:[(0,r.jsx)("div",{className:"bg-gray-200 h-4 w-3/4 rounded mb-2"}),(0,r.jsx)("div",{className:"bg-gray-200 h-3 w-1/2 rounded"})]},t))}):0===eT.length?(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)("div",{className:"w-12 h-12 bg-gray-100 rounded-xl flex items-center justify-center mx-auto mb-3",children:(0,r.jsx)("svg",{className:"w-6 h-6 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})})}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"No conversations yet"}),(0,r.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Start chatting to see your history"})]}):(0,r.jsx)(r.Fragment,{children:eT.map(e=>(0,r.jsx)(j,{chat:e,currentConversation:ea,onLoadChat:eK,onDeleteChat:eY},e.id))})}),eA&&(0,r.jsx)("div",{className:"px-4 py-2 bg-orange-50 border-t border-orange-100",children:(0,r.jsxs)("div",{className:"flex items-center text-xs text-orange-600",children:[(0,r.jsx)("svg",{className:"w-3 h-3 mr-1 animate-spin",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),"Updating..."]})}),e_&&(0,r.jsx)("div",{className:"px-4 py-2 bg-red-50 border-t border-red-100",children:(0,r.jsxs)("div",{className:"flex items-center justify-between text-xs text-red-600",children:[(0,r.jsx)("span",{children:"Failed to load history"}),(0,r.jsx)("button",{onClick:()=>eO(!0),className:"text-red-700 hover:text-red-800 font-medium",children:"Retry"})]})})]})}),(0,r.jsx)("div",{className:"fixed top-20 right-4 z-40 transition-all duration-300 ease-in-out ".concat(ee?"opacity-100 scale-100 translate-x-0":"opacity-0 scale-95 translate-x-4 pointer-events-none"),children:(0,r.jsx)("button",{onClick:()=>et(!1),className:"p-3 bg-white border border-gray-200/50 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 text-gray-600 hover:text-orange-600 hover:scale-105","aria-label":"Show history sidebar",children:(0,r.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})})})]})}j.displayName="ChatHistoryItem"}},e=>{var t=t=>e(e.s=t);e.O(0,[8888,1459,5738,9968,6060,6308,4755,563,2662,8669,4703,622,2432,408,6642,7706,7544,2138,4518,9248,2324,7358],()=>t(24766)),_N_E=e.O()}]);